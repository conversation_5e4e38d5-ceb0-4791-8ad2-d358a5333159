import React from 'react';
import { connect } from 'react-redux';
import { getKeys, applicationDetails } from '../../actions/kongCredentials';
import { getAllSources } from '../../actions/integrations';
import config from './../../config';
import {
  createKongCredentialModal,
  editKongCredentialModal,
  openConfirmModal
} from '../../actions/overlayActions';
import * as actionTypes from '../../constants/actionTypes'; 
import {
  Row,
  Col,
  Grid,
  Panel,
  Table,
  PanelBody,
  PanelContainer,
  Button,
  Tab,
  Tabs,
  OverlayTrigger,
  Tooltip
} from '@sketchpixy/rubix';
import Spinner from './../common/spinner';
import { translatr } from '@translatr/utils';
import Alert from './../common/overlay/Alert.jsx';
import { deleteCredentials } from 'edc-web-sdk/requests/kongCredentials';
import getOrgConfig from '../../actions/getConfig';
import _ from 'lodash';

let INITIAL_STATE = {
  kong_credentials: [],
  kong_application: '',
  inputType: "password"
};

@connect((state) => state)
export default class KongCredentialContainer extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.state = INITIAL_STATE;
    this.create = this.create.bind(this);
  }

  componentDidMount() {
    this.props.dispatch(getKeys());
  }

  create(e) {
    e.preventDefault();
    this.props.dispatch(createKongCredentialModal());
  }

  editHandler(id, e) {
    e && e.preventDefault();
    // Use the action creator that returns a dispatch function
    this.props.dispatch(applicationDetails(id))
      .then((application) => {
        this.props.dispatch(editKongCredentialModal(application));
      })
      .catch(err => {
        console.error("Error fetching application details:", err);
        this.props.dispatch({ 
          type: actionTypes.SHOW_ALERT, 
          message: "Error fetching application details", 
          page: "kongCredentials", 
          alertType: "error"
        });
      });
  }
  deleteHandler(id, e) {
    e && e.preventDefault();
    if (!!id) {
      const data = {
        title: translatr('common.common', 'DeleteLearningExperienceApisCredentials'),
        text: translatr('common.common', 'DeleteCredentialsConfirmation'),
        confirmText: translatr('common.common', 'delete'),
        func: func.bind(this),
        actionArgs: [id]
      };
      this.props.dispatch(openConfirmModal(data));
      function func() {
        this.props.dispatch({ type: actionTypes.SHOW_ALERT, message: "Deleting credentials...", page: "kongCredentials" });
        deleteCredentials(id).then(data => {
          this.props.dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
          this.props.dispatch({ type: actionTypes.SHOW_ALERT, message: data.message || "Credentials deleted successfully", page: "kongCredentials" });
          this.props.dispatch(getKeys()).then(() => {
            setTimeout(() => {
              this.props.dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
            }, 1000);
          });
        }).catch(err => {
          this.props.dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
          const errorMessage = err.response?.body?.message?.message || "Error deleting credentials";
          this.props.dispatch({ 
            type: actionTypes.SHOW_ALERT, 
            message: errorMessage, 
            page: "kongCredentials", 
            alertType: "error"
          });
          setTimeout(() => {
            this.props.dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
          }, 3000);
        });
      }
    }
  }

  showPassword(index, e) {
    e && e.preventDefault();
    let keyType = document.querySelector("#kongApiKey"+index).getAttribute('type') === 'password' ? 'text' : 'password';
    let secretType = document.querySelector("#kongApiSecret"+index).getAttribute('type') === 'password' ? 'text' : 'password';
    document.querySelector("#kongApiKey"+index).setAttribute('type', keyType); 
    document.querySelector("#kongApiSecret"+index).setAttribute('type', secretType);
  }

  getConfigValue(configs, slug, defaultValue){
    const result = _.find(configs, { 'slug': slug });
    const value = result ? _.get(result, 'value') : defaultValue;
    return value;
  }

  render() {
    const data = this.props.kongCredentials?.kongCredentials || {};
    const credentials = data?.data || [];
    const configs = data['configs'] || [];
    const maxAppValue = this.getConfigValue(configs, 'max_kong_credentials', 1);
    const disableCreateCredentialsBtn = credentials.length >= maxAppValue;
    const maxAppTooltip = (<Tooltip id="max-app-tooltip">{translatr('common.common', 'MaxAppTooltip')}</Tooltip>);
    
    if (window?.enable_api_gateway_credentials !== true) {
      return (<p className="unauthorized">{translatr('common.common', 'youAreUnauthorizedToViewThisPagePleaseContactYourA')}</p>)
    }
    
    return (
      <Tabs defaultActiveKey={1} animation={false} id="uncontrolled-tab-example" onSelect={() => { this.forceUpdate()}}>
        <Tab eventKey={1} title="Developer API - V6">
          <PanelContainer controls={false} className="ecl-configurations" containerClasses="new-page-container">
            <Panel>
              <PanelBody>
              <Alert isShow={this.props.overlayReducer.alert.page === "kongCredentials" && this.props.overlayReducer.alert.isShow}
                type={this.props.overlayReducer.alert.type}
                message={this.props.overlayReducer.alert.message}/>
                <Grid>
                  <Row>
                    <Col sm={8}>
                        <h5><a className="cm-link" href={config.ApiV6DocURL} target="_blank" rel='noopener noreferrer'>API Documentation</a></h5>
                    </Col>
                    <Col sm={4}>
                      <Button className="new-button pull-right button-align" onClick={this.create}>
                        Create Credentials
                      </Button>
                    </Col>
                  </Row>
                  <Row>
                   { this.props.kongCredentials?.loaded ?
                      <Col sm={12}>
                        <Table
                          ref={node => this._table = node}
                          className='display cell-border dataTable no-footer'
                          cellSpacing='0' width='100%'>
                          <thead>
                            <tr>
                              <th>{translatr('common.common', 'name')}</th>
                              <th>{translatr('common.common', 'displayName2')}</th>
                              <th>{translatr('common.common', 'apiKey')}</th>
                              <th>{translatr('common.common', 'secretKey')}</th>
                              <th>{translatr('common.common', 'actions')}</th>
                            </tr>
                          </thead>
                          <tbody>
                            { credentials.length > 0 ? credentials.map((cred, index) => {
                              return (
                                <tr className="new-table-row" key={index + 1}>
                                  <td>{cred.name}</td>
                                  <td className="text-break-all">{cred.source_display_name}</td>
                                  <td>
                                    <input type={this.state.inputType} 
                                           id={'kongApiKey' + index} 
                                           value={cred.api_key} 
                                           disabled 
                                           className="wso2-credentials" 
                                           size="32"/>
                                  </td>
                                  <td>
                                    <input type={this.state.inputType} 
                                           id={'kongApiSecret' + index} 
                                           value={cred.api_secret} 
                                           disabled 
                                           className="wso2-credentials" 
                                           size="32" />
                                  </td>
                                  <td className="dt-center">
                                    <a href="#" onClick={this.editHandler.bind(this, cred.application_id)}>
                                      <i className="icon-stroke-gap-icons-Edit rubix-icon margin-left-10" />
                                    </a>
                                    <a href="#" onClick={this.showPassword.bind(this, index)}>
                                      <i className="icon-stroke-gap-icons-Eye rubix-icon margin-left-10"></i>
                                    </a>
                                    <a href="#" onClick={this.deleteHandler.bind(this, cred.application_id)}>
                                      <i className="icon-stroke-gap-icons-Delete rubix-icon margin-left-10" />
                                    </a>
                                  </td>
                                </tr>
                              );
                            })
                            : <tr><td colSpan={5} className="textCenter">{translatr('common.common', 'noDataAvailable')}</td></tr>
                          }
                          </tbody>
                        </Table>
                      </Col>
                    :
                      <Spinner/>
                    }
                  </Row> 
                </Grid>
              </PanelBody>
            </Panel>
          </PanelContainer>
        </Tab>
      </Tabs>
    ); 
  } 
}