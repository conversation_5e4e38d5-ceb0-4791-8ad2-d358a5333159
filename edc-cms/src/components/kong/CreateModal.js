import { translatr } from '@translatr/utils';
import React from 'react';
import { connect } from 'react-redux';
import Select, { components, Async } from 'react-select';
import { closeKongCredentialModal } from '../../actions/overlayActions';
import { fetchScopesBySubscriptions, filterSelectedScopes } from '../wso2/utils/wso2Helper'; 
import FocusLock from 'react-focus-lock';
import {
  Button,
  Modal,
  FormGroup,
  Form,
  ControlLabel,
  HelpBlock,
  Grid,
  Row,
  Col
} from '@sketchpixy/rubix';
import { createCredentials }  from '../../actions/kongCredentials';
import {getCmsOrgDetails} from 'edc-web-sdk/requests/orgSettings.v2';
import { getSubscriptions, throttling as getThrottling } from 'edc-web-sdk/requests/kongCredentials';
import { scopes, application_owners } from 'edc-web-sdk/requests/wso2Credentials';
import { getAllSources } from 'edc-web-sdk/requests/ecl';
import { searchForUsersData } from "edc-web-sdk/requests/users.v2";

const INITIAL_STATE = {
  name: '',
  throttlingOptions: [],
  selectedThrottling: null,
  eclSources: [],
  subscriptions: [],
  sourceOptions: [],
  subscriptionOptions: [],
  selectedSource: null,
  selectedSubscription: [],
  orgHostName: '',
  applicationOwnerOptions: [],
  selectedOwner: null,
  scopeOptions: [],
  selectedScope: [],
  scopesFilteredBySubscriptions: [],
  dropdownVisible: true
};

const selectAllOption = {
  value: 'selectAll',
  label: 'Select All',
};

@connect((state) => state)
export default class CreateModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = INITIAL_STATE;
  }

  componentDidMount() {
    getCmsOrgDetails().then((details) => {
      const hostName = details.homePage.replace(/(^\w+:|^)\/\//, '');
      this.setState({orgHostName: hostName});
    });

    getAllSources({ is_enabled: true }).then((sources) => {
      const options = sources.map((item) => { 
        return { value: item.id, label: item.display_name } 
      });
      this.setState({sourceOptions: options});
    });

    // Use Kong's getSubscriptions API
    getSubscriptions().then((response) => {
      // Transform the response to match the format expected by the Select component
      const subscriptionOptions = response.categories.map(category => ({
        value: category.label,
        label: category.label
      }));
      this.setState({subscriptionOptions: subscriptionOptions});
    });

    // Use Kong's getThrottling API
    getThrottling().then((response) => {
      // Transform the response to match the format expected by the Select component
      const throttlingOptions = response.throttlings.map(throttling => ({
        value: throttling,
        label: throttling
      }));
      // Set default throttling to 100PerMin or the first option if available
      const defaultThrottling = throttlingOptions.find(option => option.value === '100PerMin') || 
                               (throttlingOptions.length > 0 ? throttlingOptions[0] : null);
      this.setState({
        throttlingOptions: throttlingOptions,
        selectedThrottling: defaultThrottling
      });
    });

    scopes({include_hierarchy: true}).then((scope) => {
      this.setState({scopeOptions: scope});
    });

    application_owners().then((users) => {
      const owners = users.map((user) => { 
        return { value: user[1], label: user[0] } 
      });
      this.setState({applicationOwnerOptions: owners});
    });
  }

  closeModal = () => {
    this.props.dispatch(closeKongCredentialModal());
  }

  createCredentials = (e) => {
    e.preventDefault();
    const payload = {
      name: this.state.name,
      throttling: this.state.selectedThrottling ? this.state.selectedThrottling.value : '100PerMin',
      application_owner: this.state.selectedOwner ? this.state.selectedOwner.value : null,
      source_id: this.state.selectedSource ? this.state.selectedSource.value : null,
      subscriptions: this.state.selectedSubscription 
        ? this.state.selectedSubscription.map(i => i.value)
        : [],
      scopes: this.state.selectedScope 
        ? this.state.selectedScope
            .filter(option => option.value !== 'selectAll')
            .map(i => i.value)
        : []
    };
    this.props.dispatch(createCredentials(payload));
    this.closeModal();
  }
  handleSourceChange = (event) => {
    this.setState({ selectedSource: event });
  }

  handleSubscriptionChange = (event) => {
    this.setState({ selectedSubscription: event });
    const updatedScopes = fetchScopesBySubscriptions(event, this.state.scopeOptions);
    this.setState({ scopesFilteredBySubscriptions: updatedScopes }); 
    if (this.state.selectedScope && this.state.selectedScope.length > 0) {
      const filteredScopesSelection = filterSelectedScopes(updatedScopes, this.state.selectedScope);
      this.setState({selectedScope: filteredScopesSelection});
    }
  }

  handleThrottlingChange = (event) => {
    this.setState({ selectedThrottling: event });
  }

  handleName = (event) => {
    this.setState({name: event.target.value});
  }

  handleOwnerChange = (event) => {
    this.setState({selectedOwner: event});
  }

  handleScopeChange = (event) => {
    this.setState({ selectedScope: event });
  }

  selectAllHandler = () => {
    this.setState({ 
      selectedScope: this.state.scopesFilteredBySubscriptions,
      dropdownVisible: false
    });
    
    // Set a timeout to restore dropdown visibility for future interactions
    setTimeout(() => {
      this.setState({ dropdownVisible: true });
    }, 100);
  }

  CustomSelectAllOption = (props) => {
    if (props.data.value === 'selectAll') {
      return (
        <div onClick={this.selectAllHandler} style={{ cursor: 'pointer', padding: '8px', paddingLeft: '14px' }}>
          Select All
        </div>
      );
    }
    return <components.Option {...props} />;
  }

  // Custom Menu component to respect dropdownVisible state
  CustomMenu = (props) => {
    return this.state.dropdownVisible ? <components.Menu {...props} /> : null;
  }

  getUsers = (q, callback) => {
    return searchForUsersData({q: q, limit: 10, offset: 0, fields: 'first_name, last_name, id', permission: "DEVELOPER"}).then(data => {
      const userSuggs = data.users.map((user) => {
        return { value: user.id, label: user.firstName + " " + user.lastName }
      });
      callback(userSuggs);
    });
  }

  render() {
    const creating = Boolean(this.props.overlayReducer?.modal?.isOpen);
    const { name, selectedSource, selectedSubscription, selectedThrottling, selectedOwner, selectedScope, 
            sourceOptions, subscriptionOptions, applicationOwnerOptions, scopesFilteredBySubscriptions, throttlingOptions } = this.state;
    
    const isDisable = (name.length > 0 && name.indexOf(' ') !== 0) && 
                      selectedOwner && 
                      selectedScope && selectedScope.length > 0 &&
                      selectedSubscription && selectedSubscription.length > 0;
    
    const disableScopesDropdown = !selectedSubscription || selectedSubscription.length === 0;

    return (
      <Modal aria-label={translatr('common.common', 'CreateLearningExperienceApisCredentials')} 
             className="edconnect-modal"
             show={creating}
             onHide={this.closeModal}>
        <FocusLock>
        <Modal.Header closeButton>
          <Modal.Title>{translatr('common.common', 'CreateLearningExperienceApisCredentials')}</Modal.Title>
        </Modal.Header>

        <Form horizontal onSubmit={this.createCredentials}>
          <Modal.Body>
            <Grid>
              <Row>
                <Col xs={12}>
                  <FormGroup>
                    <Col componentClass={ControlLabel} md={2} sm={12}>
                      {translatr('common.common', 'name4')}<span className="required-field">*</span>
                    </Col>
                    <Col md={3} sm={12}>
                      {this.state.orgHostName}
                    </Col>
                    <Col md={7} sm={12}>
                      <input type="text"
                             required
                             aria-label={translatr('common.common', 'enterNameForWso2mandatory')}
                             value={name}
                             className="input-settings-field description-org-textarea"
                             onChange={this.handleName}/>
                    </Col>
                  </FormGroup>
                  <FormGroup>
                    <Col componentClass={ControlLabel} md={2} sm={12}>
                      {translatr('common.common', 'source')}
                    </Col>
                    <Col md={10} sm={12}>
                      <Select options={sourceOptions}
                              value={selectedSource}
                              aria-label='kong_source'
                              onChange={this.handleSourceChange}
                              isClearable={true} />
                    </Col>
                  </FormGroup>
                  <FormGroup>
                    <Col componentClass={ControlLabel} md={2} sm={12}>
                      {translatr('common.common', 'subscription')}<span className="required-field">*</span>
                    </Col>
                    <Col md={10} sm={12}>
                      <Select options={subscriptionOptions}
                              aria-label='kong_subscription'
                              value={selectedSubscription}
                              onChange={this.handleSubscriptionChange}
                              isClearable={true}
                              isMulti={true} />
                    </Col>
                  </FormGroup>
                  <FormGroup>
                    <Col componentClass={ControlLabel} md={2} sm={12}>
                      {translatr('common.common', 'Throttling')}<span className="required-field">*</span>
                    </Col>
                    <Col md={10} sm={12}>
                      <Select options={throttlingOptions}
                              value={selectedThrottling}
                              aria-label='kong_throttling'
                              onChange={this.handleThrottlingChange}
                              isClearable={false} />
                    </Col>
                  </FormGroup>
                  <FormGroup>
                    <Col componentClass={ControlLabel} md={2} sm={12}>
                      {translatr('common.common', 'ApplicationOwner')}<span className="required-field">*</span>
                    </Col>
                    <Col md={10} sm={12}>
                      <Async
                          isMulti={false}
                          options={applicationOwnerOptions}
                          value={selectedOwner}
                          aria-label='kong_owner'
                          onChange={this.handleOwnerChange}
                          isClearable={true}
                          loadOptions={this.getUsers}
                          required />
                    </Col>
                  </FormGroup>
                  <FormGroup>
                    <Col componentClass={ControlLabel} md={2} sm={12}>
                      {translatr('common.common', 'Scope')}<span className="required-field">*</span>
                    </Col>
                    <Col md={10} sm={12}>
                      <Select
                        options={[ selectAllOption, ...scopesFilteredBySubscriptions]}
                        aria-label='kong_scopes'
                        onChange={this.handleScopeChange}
                        components={{ 
                          Option: this.CustomSelectAllOption,
                          Menu: this.CustomMenu
                        }}
                        value={selectedScope}
                        isClearable={true}
                        isMulti={true}
                        required
                        isDisabled={disableScopesDropdown}
                        placeholder={disableScopesDropdown ? 
                          translatr('common.common', 'SelectSubscriptionForScopeList') : 
                          `${translatr('common.common', 'select')}...`} 
                      />
                    </Col>
                  </FormGroup>
                </Col>
              </Row>
            </Grid>
            <HelpBlock>
              <span className="tooltip-item tooltip-help-block">i</span>
              <i>Enter the name and click 'Generate' to create the default organization credentials</i>
            </HelpBlock>
          </Modal.Body>

          <Modal.Footer>
            <div className="inline-layout right">
              <Button onClick={this.closeModal}>{translatr('common.common', 'cancel')}</Button>
              <Button type="submit" disabled={!isDisable}>{translatr('common.common', 'generate')}</Button>
            </div>
          </Modal.Footer>
        </Form>
        </FocusLock>
      </Modal>
    );
  }
}