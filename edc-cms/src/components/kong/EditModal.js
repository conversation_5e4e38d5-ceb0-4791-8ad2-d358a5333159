import { translatr } from '@translatr/utils';
import React from 'react';
import { connect } from 'react-redux';
import Select, { components } from 'react-select';
import { closeKongCredentialModal } from '../../actions/overlayActions';
import { updateCredentials } from '../../actions/kongCredentials';
import FocusLock from 'react-focus-lock';
import { getAllSources } from 'edc-web-sdk/requests/ecl';
import { getSubscriptions } from 'edc-web-sdk/requests/kongCredentials';
import { scopes, application_owners } from 'edc-web-sdk/requests/wso2Credentials';
import { fetchScopesBySubscriptions, filterSelectedScopes } from '../wso2/utils/wso2Helper';
import {
  Button,
  Modal,
  FormGroup,
  Form,
  ControlLabel,
  Grid,
  Row,
  Col
} from '@sketchpixy/rubix';

const selectAllOption = {
  value: 'selectAll',
  label: 'Select All',
};

@connect((state) => state)
export default class EditModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      sourceOptions: [],
      selectedSource: null,
      subscriptionOptions: [],
      selectedSubscription: [],
      applicationOwnerOptions: [],
      selectedOwner: null,
      scopeOptions: [],
      selectedScope: [],
      scopesFilteredBySubscriptions: [],
      dropdownVisible: true
    };
    this.closeModal = this.closeModal.bind(this);
    this.updateCredentials = this.updateCredentials.bind(this);
    this.handleSubscriptionChange = this.handleSubscriptionChange.bind(this);
    this.handleScopeChange = this.handleScopeChange.bind(this);
    this.selectAllHandler = this.selectAllHandler.bind(this);
    this.CustomSelectAllOption = this.CustomSelectAllOption.bind(this);
  }

  componentDidMount() {
    // Fetch source options for the dropdown
    getAllSources().then((sources) => {
      const sourcesList = sources.map((item) => { 
        return { value: item.id, label: item.display_name } 
      });
      this.setState({sourceOptions: sourcesList});
      
      // If the application has a source_id, set the selected source
      if (this.props.data && this.props.data.source_id) {
        const setSourceId = sourcesList.filter((item) => item.value == this.props.data.source_id);
        this.setState({ selectedSource: setSourceId.length > 0 ? setSourceId[0] : null });
      }
    }).catch(err => {
      // Handle error silently
    });

    // Fetch subscription options using Kong's API
    getSubscriptions().then((response) => {
      // Transform the response to match the format expected by the Select component
      const subscriptionOptions = response.categories.map(category => ({
        value: category.label,
        label: category.label
      }));
      
      this.setState({subscriptionOptions: subscriptionOptions});
      
      // If the application has subscriptions, set the selected subscriptions
      if (this.props.data && this.props.data.subscriptions) {
        const setSubscriptions = this.props.data.subscriptions.map(item => ({
          value: item,
          label: item
        }));
        this.setState({selectedSubscription: setSubscriptions});
      }
    }).catch(err => {
      // Handle error silently
    });

    // Fetch application owners
    application_owners().then((users) => {
      const owners = users.map((user) => { return { value: user[1], label: user[0] } });
      this.setState({applicationOwnerOptions: owners});
      
      // If the application has a user_id, set the selected owner
      if (this.props.data && this.props.data.user_id) {
        const setOwner = owners.filter((item) => item.value == this.props.data.user_id);
        this.setState({ selectedOwner: setOwner.length > 0 ? setOwner[0] : null });
      } else if (this.props.data && this.props.data.application_owner) {
        // If user_id is not available, try to match by application_owner
        const setOwner = owners.filter((item) => item.value == this.props.data.application_owner);
        this.setState({ selectedOwner: setOwner.length > 0 ? setOwner[0] : null });
      }
    }).catch(err => {
      // Handle error silently
    });

    // Fetch scopes using WSO2's API
    scopes({include_hierarchy: true}).then((scope) => {
      this.setState({scopeOptions: scope});
      
      // If the application has scopes, set the selected scopes
      if (this.props.data && this.props.data.scopes) {
        const selectedScopes = this.props.data.scopes.map((item) => {
          return { value: item, label: item };
        });
        this.setState({selectedScope: selectedScopes});
        
        // Also update filtered scopes based on selected subscriptions
        if (this.state.selectedSubscription.length > 0) {
          const updatedScopes = fetchScopesBySubscriptions(this.state.selectedSubscription, scope);
          this.setState({scopesFilteredBySubscriptions: updatedScopes});
        }
      }
    }).catch(err => {
      // Handle error silently
    });
  }

  closeModal() {
    this.props.dispatch(closeKongCredentialModal());
  }

  handleSubscriptionChange(event) {
    this.setState({ selectedSubscription: event });
    const updatedScopes = fetchScopesBySubscriptions(event, this.state.scopeOptions);
    this.setState({ scopesFilteredBySubscriptions: updatedScopes }); 
    if (this.state.selectedScope && this.state.selectedScope.length > 0) {
      const filteredScopesSelection = filterSelectedScopes(updatedScopes, this.state.selectedScope);
      this.setState({selectedScope: filteredScopesSelection});
    }
  }

  handleScopeChange(event) {
    this.setState({ selectedScope: event });
  }

  selectAllHandler() {
    this.setState({ 
      selectedScope: this.state.scopesFilteredBySubscriptions,
      dropdownVisible: false
    });
    
    // Set a timeout to restore dropdown visibility for future interactions
    setTimeout(() => {
      this.setState({ dropdownVisible: true });
    }, 100);
  }

  CustomSelectAllOption(props) {
    if (props.data.value === 'selectAll') {
      return (
        <div onClick={this.selectAllHandler} style={{ cursor: 'pointer', padding: '8px', paddingLeft: '14px' }}>
          Select All
        </div>
      );
    }
    return <components.Option {...props} />;
  }

  // Custom Menu component to respect dropdownVisible state
  CustomMenu = (props) => {
    return this.state.dropdownVisible ? <components.Menu {...props} /> : null;
  }

  updateCredentials(e) {
    e.preventDefault();
    
    if (!this.props.data || !this.props.data.application_id) {
      return;
    }
    
    const payload = {
      id: this.props.data.application_id,
      name: this.props.data.name,
      subscriptions: this.state.selectedSubscription.map(i => i.value),
      scopes: this.state.selectedScope
        .filter(option => option.value !== 'selectAll')
        .map(i => i.value)
    };
    
    this.props.dispatch(updateCredentials(payload));
    this.closeModal();
  }

  render() {
    if (!this.props.data) {
      return null;
    }
    
    // Use the same translation key as WSO2 edit form
    const edconnectLabel = translatr('common.common', 'EditLearningExperienceApisCreadentials');
    const { sourceOptions, selectedSource, subscriptionOptions, selectedSubscription, 
            applicationOwnerOptions, selectedOwner, selectedScope, scopesFilteredBySubscriptions } = this.state;
    
    const isDisable = (selectedSubscription.length !== 0 && selectedScope.length !== 0);
    const disableScopesDropdown = selectedSubscription.length === 0;

    return (
      <Modal aria-label={edconnectLabel} className="edconnect-modal"
             show={true}
             onHide={this.closeModal}>
        <FocusLock>
          <Modal.Header closeButton>
            <Modal.Title>{translatr('common.common', 'EditLearningExperienceApisCreadentials')}</Modal.Title>
          </Modal.Header>

          <Form horizontal onSubmit={this.updateCredentials}>
            <Modal.Body>
              <Grid>
                <Row>
                  <Col xs={12}>
                    <FormGroup>
                      <Col componentClass={ControlLabel} md={2} sm={12}>
                        {translatr('common.common', 'name4')}<span className="required-field">*</span>
                      </Col>
                      <Col md={10} sm={12}>
                        <input type="text"
                           required
                           aria-label={translatr('common.common', 'enterNameForWso2mandatory')}
                           value={this.props.data.name || ''}
                           className="input-settings-field description-org-textarea"
                           disabled={true}
                        />
                      </Col>
                    </FormGroup>
                    <FormGroup>
                      <Col componentClass={ControlLabel} md={2} sm={12}>
                        {translatr('common.common', 'source')}
                      </Col>
                      <Col md={10} sm={12}>
                        <Select options={sourceOptions}
                                value={selectedSource}
                                aria-label='edconnect_source'
                                isDisabled={true}
                                 />
                      </Col>
                    </FormGroup>
                    <FormGroup>
                      <Col componentClass={ControlLabel} md={2} sm={12}>
                        {translatr('common.common', 'subscription')}<span className="required-field">*</span>
                      </Col>
                      <Col md={10} sm={12}>
                        <Select options={subscriptionOptions}
                                value={selectedSubscription}
                                aria-label='subscriptions'
                                onChange={this.handleSubscriptionChange}
                                isClearable={true}
                                isMulti={true} />
                      </Col>
                    </FormGroup>
                    <FormGroup>
                      <Col componentClass={ControlLabel} md={2} sm={12}>
                        {translatr('common.common', 'Throttling')}
                      </Col>
                      <Col md={10} sm={12}>
                        <input type="text"
                               value={this.props.data.throttling || ''}
                               className="input-settings-field description-org-textarea"
                               disabled={true}/>
                      </Col>
                    </FormGroup>
                    <FormGroup>
                      <Col componentClass={ControlLabel} md={2} sm={12}>
                        {translatr('common.common', 'ApplicationOwner')}
                      </Col>
                      <Col md={10} sm={12}>
                        <Select options={applicationOwnerOptions}
                                value={selectedOwner}
                                aria-label='edconnect_owner'
                                isClearable={false}
                                isDisabled={true}
                                required />
                      </Col>
                    </FormGroup>
                    <FormGroup>
                      <Col componentClass={ControlLabel} md={2} sm={12}>
                        {translatr('common.common', 'Scope')}<span className="required-field">*</span>
                      </Col>
                      <Col md={10} sm={12}>
                        <Select
                          options={[selectAllOption, ...scopesFilteredBySubscriptions]}
                          aria-label='edconnect_scopes'
                          value={selectedScope}
                          onChange={this.handleScopeChange}
                          isClearable={true}
                          isMulti={true}
                          isDisabled={disableScopesDropdown}
                          components={{ 
                            Option: this.CustomSelectAllOption,
                            Menu: this.CustomMenu
                          }}
                          placeholder={disableScopesDropdown ? 
                            translatr('common.common', 'SelectSubscriptionForScopeList') : 
                            `${translatr('common.common', 'select')}...`}
                          required
                        />
                      </Col>
                    </FormGroup>
                  </Col>
                </Row>
              </Grid>
            </Modal.Body>

            <Modal.Footer>
              <div className="inline-layout right">
                <Button onClick={this.closeModal}>{translatr('common.common', 'cancel')}</Button>
                <Button type="submit" disabled={!isDisable}>{translatr('common.common', 'update')}</Button>
              </div>
            </Modal.Footer>
          </Form>
        </FocusLock>
      </Modal>
    );
  }
}
