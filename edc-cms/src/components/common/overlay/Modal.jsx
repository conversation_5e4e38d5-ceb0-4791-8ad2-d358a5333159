import React, {Component, PropTypes} from 'react';
import ReactModal from 'react-modal';
import {connect} from 'react-redux';
import {closeModal} from '../../../actions/overlayActions';
import * as modalTypes from '../../../constants/modalTypes';
import * as modalSizes from '../../../constants/modalSizes';
import SourceModal from '../../sources/SourceModal.jsx';
import ConfirmModal from './modals/ConfirmModal.jsx';
import MessageModal from './modals/MessageModal.jsx';
import ProviderModal from '../../Integrations/ProviderModal.jsx';
import BoxProviderModal from '../../Integrations/BoxProviderModal.jsx';
import SharepointTokenBasedProviderModal from '../../Integrations/SharepointTokenBasedProviderModal.jsx'
import SourcesModal from '../../Integrations/SourcesModal.jsx';
import DeleteSourceModal from '../../Integrations/DeleteSourceModal.jsx';
import DeleteLmsSourceModal from '../../Integrations/DeleteLmsSourceModal.jsx';
import AddCollabConfirmModal from '../../channels/AddCollabConfirmModal.jsx';
import UserImportDisplayStatus from '../../UserImportStatus/DisplayStatusModal.jsx';
import WorkflowDisplayStatus from '../../workflow/ViewWorkflowModal.jsx';
import AddCustomFieldModal from '../../settings/AddCustomFieldModal.jsx';
import LmsProviderModal from '../../Integrations/LmsProviderModal.jsx';
import PartnerCenterModal from '../../Integrations/PartnerCenterModal.jsx';
import ReportModal from '../../Integrations/ReportModal.js';
import ContentCountModal from '../../Integrations/ContentCountModal.js';
import ArchiveProviderModal from '../../Integrations/ArchiveProviderModal.jsx';
import HrmsUsersModal from '../../Integrations/HrmsUsersModal.jsx';
import OAuth2Modal from '../../Integrations/OAuth2Modal.js';
import ArchiveLmsProviderModal from '../../Integrations/ArchiveLmsProviderModal.jsx';
import ClcEditModal from '../../settings/clcEditModal';
import ClcDeleteModal from '../../settings/clcDeleteModal';
import ClcActiveInactiveModal from '../../settings/clcActiveInactiveModal';
import SourceExceptionLogsModal from '../../Integrations/SourceExceptionLogsModal.jsx';
import DynamicWorkflowDeleteModal from '../../workflow/dynamic/modals/DynamicWorkflowDeleteModal.jsx';
import DynamicWorkflowViewModal from '../../workflow/dynamic/modals/DynamicWorkflowViewModal.jsx';
import MkpUserModal from '../../Users/<USER>/MkpUserModal.jsx';
import MkpDeleteModal from '../../Users/<USER>/MkpDeleteModal.jsx';
import JobsActionModal from '../../HRData/shared/components/JobsActionModal';
import LmsConfigModal from '../../Integrations/LmsConfigModal';
import CreateModal from '../../wso2/CreateModal.js';
import EditModal from '../../wso2/EditModal.js';
import MediaUpload from '../../MediaUpload/MediaUpload.js';
import ContentImportStatusModal from '../../ContentImportStatus/ContentImportStatusModal.jsx';
import KongCreateModal from '../../kong/CreateModal';
import KongEditModal from '../../kong/EditModal';

class Modal extends Component {
    constructor(props, context) {
        super(props, context);
        this.cancelClickHandler = this.cancelClickHandler.bind(this);
        this._modalStyle = {
            overlay: {
                overflowY: "scroll",
                backgroundColor: "rgba(0,0,0,0.5)"
            },
            content: {
                top     : '80px',
                left    : "none",
                right   : 'auto',
                bottom  : 'auto',
                overflow: 'visible'
            }
        }
    }

    cancelClickHandler() {
        this.props.overlayReducer.modal.dispatch(closeModal());
    }
    render() {
        let modal;
        let modalSize = modalSizes.LARGE;
        if (this.props.overlayReducer.modal.isOpen) {
            switch (this.props.overlayReducer.modal.type) {
                case modalTypes.KONG_CREDENTIAL_CREATE:
                    modal = <KongCreateModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type} />;
                    break;
                case modalTypes.ADD_NEW_SOURCE:
                case modalTypes.EDIT_SOURCE:
                    modal = <SourceModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.LARGE;
                    break;
                case modalTypes.PROVIDER_MODAL:
                    modal = <ProviderModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.ARCHIVE_PROVIDER_MODAL:
                    modal = <ArchiveProviderModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.SOURCE_MODAL:
                    modal = <SourcesModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.SOURCE_REPORT_MODAL:
                    modal = <ReportModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.CONTENT_COUNT_MODAL:
                    modal = <ContentCountModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.DELETE_SOURCE:
                    modal = <DeleteSourceModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.DELETE_LMS_SOURCE:
                    modal = <DeleteLmsSourceModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.CONFIRM:
                    modal = <ConfirmModal data={this.props.overlayReducer.modal.data} isOpen={this.props.overlayReducer.modal.isOpen}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.JOBS_ACTION:
                    modal = <JobsActionModal data={this.props.overlayReducer.modal.data} isOpen={this.props.overlayReducer.modal.isOpen} />
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.MESSAGE:
                    modal = <MessageModal data={this.props.overlayReducer.modal.data}/>;
                    modalSize = modalSizes.LARGE;
                    break;
                case modalTypes.LMS_PROVIDER_MODAL:
                    modal = <LmsProviderModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.ADD_COLLAB_CONFIRM:
                    modal = <AddCollabConfirmModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.CUSTOM_FIELD_MODAL:
                    modal = <AddCustomFieldModal existNames={this.props.overlayReducer.modal.data}/>;
                    break;
                case modalTypes.BOX_PROVIDER_MODAL:
                    modal = <BoxProviderModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.SHAREPOINT_TOKEN_BASED_PROVIDER_MODAL:
                    modal = <SharepointTokenBasedProviderModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.PARTNER_CENTER_MODAL:
                    modal = <PartnerCenterModal data={this.props.overlayReducer.modal.data} />;
                    break;
                case modalTypes.OPEN_USER_IMPORT_STATUS:
                    modal = <UserImportDisplayStatus data={this.props.overlayReducer.modal.data} />;
                    break;
                case modalTypes.OPEN_WORKFLOW_STATUS:
                    modal = <WorkflowDisplayStatus data={this.props.overlayReducer.modal.data} />;
                    break;
                case modalTypes.HRMS_USERS_MODAL:
                    modal = <HrmsUsersModal data={this.props.overlayReducer.modal.data} />;
                    break;
                case modalTypes.OAUTH2_MODAL:
                    modal = <OAuth2Modal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type} />;
                    break;
                case modalTypes.LMS_ARCHIVE_MODAL:
                    modal = <ArchiveLmsProviderModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    modalSize = modalSizes.SMALL;
                    break;
                case modalTypes.OPEN_CLC_EDIT_MODAL:
                    modal= <ClcEditModal data={this.props.overlayReducer.modal.data} />
                    break;
                case modalTypes.OPEN_DELETE_CLC_MODAL:
                    modal= <ClcDeleteModal data={this.props.overlayReducer.modal.data} />
                    break;
                case modalTypes.OPEN_ACTIVE_INACTIVE_CLC_MODAL:
                    modal= <ClcActiveInactiveModal data={this.props.overlayReducer.modal.data} />
                    break;
                case modalTypes.SOURCE_EXCEPTION_LOGS_MODAL:
                    modal = <SourceExceptionLogsModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                    break;
                case modalTypes.OPEN_DYNAMIC_GROUP_DELETE_MODAL:
                    modal = <DynamicWorkflowDeleteModal data={this.props.overlayReducer.modal.data}/>;
                    break;
                case modalTypes.OPEN_DYNAMIC_GROUP_VIEW_MODAL:
                    modal = <DynamicWorkflowViewModal data={this.props.overlayReducer.modal.data}/>;
                    break;
                case modalTypes.OPEN_ADD_MKP_ADMIN_USER_MODAL:
                    modal = <MkpUserModal />;
                    break;
                case modalTypes.OPEN_DELETE_MKP_ADMIN_USER_MODAL:
                    modal = <MkpDeleteModal data={this.props.overlayReducer.modal.data} />;
                    break;
                case modalTypes.OPEN_LMS_CONFIG_MODAL:
                    modal = <LmsConfigModal data={this.props.overlayReducer.modal.data}/>;
                    break;
                case modalTypes.CREATE_WSO2_CREDENTIAL_MODAL:
                    modal = <CreateModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type} />;
                    break;
                case modalTypes.EDIT_WSO2_CREDENTIAL_MODAL:
                    modal = <EditModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type} />;
                    break;
                case modalTypes.MEDIA_UPLOAD:
                    modal = <MediaUpload data={this.props.overlayReducer.modal.data}/>;
                    break;
                case modalTypes.OPEN_CONTENT_IMPORT_STATUS:
                    modal = <ContentImportStatusModal data={this.props.overlayReducer.modal.data} />;
                    break;
                case modalTypes.KONG_CREDENTIAL_EDIT:
                    modal = <KongEditModal data={this.props.overlayReducer.modal.modalProps} />;
                    break;
                // case modalTypes.EDIT_CONTENT:
                //     modal = <EditContentModal data={this.props.overlayReducer.modal.data}/>;
                //     modalSize = modalSizes.LARGE;
                //     break;
                // case modalTypes.TAGS:
                //     modal = <TagsModal data={this.props.overlayReducer.modal.data}/>;
                //     modalSize = modalSizes.LARGE;
                //     break;
                // case modalTypes.ADD_FILTER:
                //     modal = <AddFilter data={this.props.overlayReducer.modal.data}/>;
                //     break;
                // case modalTypes.CLONE_LIVE_STREAM:
                // case modalTypes.EDIT_LIVE_STREAM:
                //     modal = <LiveStreamModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                //     modalSize = modalSizes.LARGE;
                //     break;
                // case modalTypes.CLONE_PATHWAY:
                // case modalTypes.EDIT_PATHWAY:
                //     modal = <PathwaysModal data={this.props.overlayReducer.modal.data} type={this.props.overlayReducer.modal.type}/>;
                //     modalSize = modalSizes.LARGE;
                //     break;
                // case modalTypes.EDIT_COURSE:
                //     modal = <EditCourseModal data={this.props.overlayReducer.modal.data}/>;
                //     modalSize = modalSizes.LARGE;
                //     break;
            }
        }
        let className = "col-xs-offset-1 col-xs-10 col-md-offset-2 col-md-8 container";
        if (modalSize === modalSizes.SMALL) {
            className = "col-xs-offset-1 col-xs-10 col-md-offset-2 col-md-8 container";
        }
        return (
            <ReactModal
                contentLabel=""
                isOpen={this.props.overlayReducer.modal.isOpen}
                onRequestClose={this.cancelClickHandler}
                className={className}
                style={this._modalStyle}
            >
                <div id="cms-modal">
                    {modal}
                </div>
            </ReactModal>
        );
    }
}

Modal.propTypes = {
    type: PropTypes.string,
    isOpen: PropTypes.bool,
    data: PropTypes.object
};
export default connect(state=>state)(Modal)
