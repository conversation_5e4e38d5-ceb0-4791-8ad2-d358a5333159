import React, {Component, PropTypes} from 'react';
import {connect} from 'react-redux';
import Modal from './Modal.jsx';
import Warning from './Warning.jsx';
import KongCreateModal from '../../kong/CreateModal';
import KongEditModal from '../../kong/EditModal';

class OverlayContainer extends Component {
  constructor(props, context) {
    super(props, context);
  }

  render() {
    const { overlayReducer } = this.props;
    
    if (overlayReducer && overlayReducer.modal) {
      const modalType = overlayReducer.modal.type;
      
      if (modalType === 'KONG_CREDENTIAL_CREATE') {
        return <KongCreateModal />;
      } else if (modalType === 'KONG_CREDENTIAL_EDIT') {
        return <KongEditModal data={overlayReducer.modal.modalProps} />;
      }
    }
    
    const modal = overlayReducer?.modal || { isOpen: false, type: null, data: null };
    const warning = overlayReducer?.warning || { isShow: false, type: null, header: '', message: '' };
    
    return (
      <div className="overlay">
        <Modal
          isOpen={modal.isOpen}
          type={modal.type}
          data={modal.data}
        />
        <Warning
          isShow={warning.isShow}
          type={warning.type}
          header={warning.header}
          message={warning.message}
        />
      </div>
    );
  }
}

OverlayContainer.propTypes = {
  overlayReducer: PropTypes.shape({
    modal: PropTypes.object,
    warning: PropTypes.object
  })
};

// Only connect to the specific part of the state that we need
export default connect(state => ({ 
  overlayReducer: state.overlayReducer 
}))(OverlayContainer);
