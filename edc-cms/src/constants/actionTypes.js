OPEN_PROVIDER_MODAL/*
 * action types
 */
//user/users
export const GET_USER_INFO = 'get_user_info';
export const RECEIVE_INIT_USER_INFO = 'receive_init_user_info';
export const REMOVE_USER_ROLE = 'remove_user_role';
export const SET_SUB_ADMIN_PERMISSIONS = 'set_sub_admin_permissions';
export const UPDATE_ORG_CONFIG = 'update_org_config';


export const GET_USERS = 'get_users';

export const CLICK_STATE_BTN = 'click_state_btn';

export const SHOW_ROLE_MODAL = 'show_role_modal';
export const HIDE_ROLE_MODAL = 'hide_role_modal';

export const SHOW_INVITE_MODAL = 'show_invite_modal';
export const HIDE_INVITE_MODAL = 'hide_invite_modal';
export const HIDE_RESET_INVITE_MODAL = 'hide_reset_invite_model';

export const SHOW_USERS_EDIT_MODAL = 'show_users_edit_modal';

export const HIDE_USERS_EDIT_MODAL = 'hide_users_edit_modal';
export const HIDE_ANONYMIZE_CONFIRMATION_MODAL = 'hide_anonymize_confirmation_modal';
export const ANONYMIZED_USER = 'anonymized_user'
export const CHANGE_STATE_SUSPEND = 'change_state_suspend';
export const UPDATE_SUSPEND = 'update_suspend';
export const UPDATE_EDITED_USER = 'update_edited_user';

export const RECEIVE_NEXT_USERS     = 'receive_next_users';
export const RECEIVE_PREVIOUS_USERS = 'receive_previous_users';
export const CHANGE_LIMIT_USERS_PER_PAGE = 'change_limit_users_per_page';
export const CHANGE_ACTIVE_FILTER = 'change_active_filter';

export const TOGGLE_SUSPEND_USER_CHECK = 'toggle_susped_user_check';
export const TOGGLE_CHECK_ALL_SUSPEND_USER = 'toggle_check_all_susped_user';
export const SHOW_USER_CHECKED_CONFIRMATION_MODAL = 'show_user_checked_confirmation_modal';
export const HIDE_USER_CHECKED_CONFIRMATION_MODAL = 'hide_user_checked_confirmation_modal';
export const SAVE_USER_ORG = 'save_user_org';
export const EMPTY_USERS_CHECKED_IDS = 'empty_users_checked_ids';

export const SHOW_BULK_UPDATE_USERS_MODAL = 'show_bulk_update_users_modal';
export const HIDE_BULK_UPDATE_USERS_MODAL = 'hide_bulk_update_users_modal';

//user/role
export const GET_ROLES = 'get_roles';

export const ADD_NEW_ROLE          = 'add_new_role';
export const SHOW_ROLE_ADD_MODAL   = 'show_role_add_modal';
export const HIDE_ROLE_ADD_MODAL   = 'hide_role_add_modal';

export const SHOW_USERS_MODAL = 'show_users_modal';
export const HIDE_USERS_MODAL = 'hide_users_modal';

export const SHOW_ROLE_EDIT_MODAL = 'show_role_edit_modal';
export const HIDE_ROLE_EDIT_MODAL = 'hide_role_edit_modal';

export const SHOW_DELETE_ROLE_MODAL = 'show_delete_role_modal';
export const HIDE_DELETE_ROLE_MODAL = 'hide_delete_role_modal';

export const RECEIVE_DELETE_ROLE = 'receive_delete_role';

export const RECEIVE_NEXT_ROLES     = 'receive_next_roles';
export const RECEIVE_PREVIOUS_ROLES = 'receive_previous_roles';

export const SHOW_UPLOAD_MODAL = 'show_upload_modal';
export const HIDE_UPLOAD_MODAL = 'hide_upload_modal';

// MKP Admin
export const SHOW_ADD_MKP_ADMIN_USER = 'show_add_mkp_admin_user';
export const SHOW_DELETE_MKP_USER = 'show_delete_mkp_user';
export const HIDE_ADD_MKP_ADMIN_USER = 'hide_add_mkp_admin_user';
export const HIDE_DELETE_MKP_USER = 'hide_delete_mkp_user';
export const DELETED_MKP_ADMIN_USER = 'deleted_mkp_admin_user';
export const ADDED_MKP_ADMIN_USER = 'added_mkp_admin_user';

//export const SHOW_USERS_MODAL = 'show_users_modal';
//export const HIDE_USERS_MODAL = 'hide_users_modal';
//analytics
export const RECEIVE_ANALYTICS_DATA = 'receive_analytics_data';
export const RECEIVE_TEAM_ANALYTICS_DATA = 'receive_team_analytics_data';
export const RECEIVE_CONTENT_ANALYTICS_DATA = 'receive_content_analytics_data';
export const RECEIVE_ASSESSMENT_ANALYTICS_DATA = 'receive_assessment_analytics_data';
export const RECEIVE_ASSESSMENT_ANALYTICS_DETAILS = 'receive_assessment_analytics_details';

//channels
export const SHOW_EDIT_CHANNEL_MODAL = 'show_channel_edit_modal';
export const HIDE_EDIT_CHANNEL_MODAL = 'hide_channel_edit_modal';
export const SHOW_ADD_CHANNEL_MODAL = 'show_channel_new_modal';
export const HIDE_ADD_CHANNEL_MODAL = 'hide_channel_new_modal';
export const SET_DEFAULT_BANNER_SRC = 'default_banner_src';
export const DELETE_BANNER_SRC = 'delete_banner_src';
export const SET_CURRENT_BANNER_SRC = 'set_current_bunner_src';
export const SET_DEFAULT_PROFILE_SRC = 'set_default_profile_src';
export const DELETE_PROFILE_SRC = 'delete_profile_src';
export const SET_CURRENT_PROFILE_SRC = 'set_current_profile_src';
export const SET_CHANNEL_AUTO_FOLLOW = 'set_channel_auto_follow';
export const CREATE_CHANNEL = 'create channel';
export const UPDATE_CHANNEL = 'update_channel';
export const CHANGE_LIMIT_CHANNELS_PER_PAGE = 'change_limit_channels_per_page';
export const RECEIVE_PAGE_CHANNELS = 'receive_page_channels';
export const SHOW_CLONE_CHANNEL_MODAL = 'show_channel_clone_modal';
export const HIDE_CLONE_CHANNEL_MODAL = 'hide_channel_clone_modal';
export const CONFIRM_CLONE_CHANNEL_MODAL = 'show_confirm_channel_clone_modal';
export const HIDE_CONFIRM_CLONE_CHANNEL_MODAL = 'hide_confirm_channel_clone_modal';
export const CHOOSE_TEAM_TO_CLONE_CHANNEL = 'choose_team_to_clone_channel';
export const CONFIRM_CLONE_CHANNEL = 'confirm_clone_channel';
export const HIDE_CHANNEL_SOURCES_MODAL = 'hide_channel_sources_modal';
export const SHOW_DELETE_CHANNEL_MODAL = 'show_delete_channel_modal';
export const HIDE_DELETE_CHANNEL_MODAL = 'hide_delete_channel_modal';
export const REQUEST_UPLOAD_CHANNEL_IMAGE = 'request_upload_channel_image';
export const RECEIVE_UPLOAD_CHANNEL_IMAGE = 'receive_upload_channel_image';
export const GET_CHANNEL_SOURCE = 'get_channel_source';
export const RECEIVE_CHANNEL_COLLABORATORS = 'receive_channel_collaborators';
export const RECEIVE_CHANNEL_FOLLOWERS = 'receive_channel_followers';
export const SHOW_FOLLOWERS_MODAL = 'show_followers_modal';
export const SHOW_HISTORY_MODAL = 'show_history_modal';
export const HIDE_HISTORY_MODAL = 'hide_history_modal';
export const HIDE_COLLABORATORS_MODAL = 'hide_collaborators_modal';
export const HIDE_FOLLOWERS_MODAL = 'hide_followers_modal';
export const CHANNEL_COLLABORATORS_SEARCH = 'channel_collaborators_search';

//organizations
export const RECEIVE_INIT_ORGANIZATIONS = 'receive_init_organizations';
export const SHOW_CAROUSEL_ITEM_MODAL = 'show_carousel_item_modal';
export const HIDE_CAROUSEL_MODAL = 'hide_carousel_modal';
export const SHOW_IMAGE_EDITOR_MODAL = 'show_image_editor_modal';
export const HIDE_IMAGE_EDITOR_MODAL = 'hide_image_editor_modal';
export const SHOW_LANGUAGES_MODAL = 'show_set_languages_modal';
export const SHOW_PROFICIENCY_LANGUAGES_MODAL = 'show_proficiency_languages_modal';
export const HIDE_LANGUAGES_MODAL = 'hide_set_languages_modal';
export const HIDE_PROFICIENCY_LANGUAGES_MODAL = 'hide_proficiency_languages_modal';


//modals
export const OPEN_EDIT_MODAL    = 'open_edit_modal';
export const OPEN_DELETE_MODAL  = 'open_delete_modal';
export const HIDE_DELETE_MODAL  = 'hide_delete_modal';
export const OPEN_ADD_MODAL     = 'open_add_modal';
export const CLOSE_EDIT_MODAL   = 'close_edit_modal';
export const CLOSE_DELETE_MODAL = 'close_delete_modal';
export const CLOSE_ADD_MODAL    = 'close_add_modal';
export const JOBS_ACTION_MODAL = 'jobs_action_modal';
export const RESET_SELECTED_CONTENT = 'reset_selected_content';

/**
 *  CMS
 */
//sources
export const RECEIVE_INIT_SOURCES = 'receive_init_sources';
export const OPEN_ADD_NEW_SOURCE_MODAL = 'open_add_new_source_modal';
export const REQUEST_SAVE_NEW_SOURCE = 'request_save_new_source';
export const RECEIVE_SAVE_NEW_SOURCE = 'receive_save_new_source';
export const REQUEST_DELETE_SOURCE = 'request_delete_source';
export const RECEIVE_DELETE_SOURCE = 'receive_delete_source';
export const OPEN_EDIT_SOURCE_MODAL = 'open_edit_source_modal';
export const REQUEST_SAVE_EDIT_SOURCE = 'request_save_edit_source';
export const RECEIVE_SAVE_EDIT_SOURCE = 'receive_save_edit_source';
export const RECEIVE_SOURCE_URL_VALIDATION = 'receive_source_url_validation';
export const ERROR_SAVE_SOURCE = 'error_save_source';
export const OPEN_SOURCE_DELETE_MODAL  = 'open_source_delete_modal';
export const CLOSE_SOURCE_DELETE_MODAL = 'close_source_delete_modal';
export const CLOSE_SOURCE_MODAL = 'close_source_modal';
export const RECEIVE_SOURCE_CHANNEL_SUGGEST = 'receive_source_channel_suggest';
export const ADD_CHANNEL_TO_SOURCE_MODAL = 'add_channel_to_source_modal';
export const REMOVE_CHANNEL_FROM_SOURCE_MODAL = 'remove_channel_from_source_modal';
export const RECEIVE_PREVIOUS_SOURCES = 'receive_previous_sources';
export const RECEIVE_NEXT_SOURCES = 'receive_next_sources';
export const TOGGLE_CHECK_ALL_SOURCES = 'toggle_check_all_sources';
export const TOGGLE_SOURCE_CHECK = 'toggle_source_check';
export const CHANGE_LIMIT_SOURCES_PER_PAGE = 'change_limit_sources_per_page';



//contents
export const RECEIVE_INIT_CONTENTS = 'receive_init_contents';
export const TOGGLE_CHECK_ALL_CONTENTS = 'toggle_check_all_contents';
export const TOGGLE_CONTENT_CHECK = 'toggle_content_check';
export const REQUEST_CHANGE_CONTENT_STATE = 'request_change_content_state';
export const RECEIVE_CHANGE_CONTENT_STATE = 'receive_change_content_state';
export const RECEIVE_EDIT_CONTENT = 'receive_edit_content';
export const UPDATE_CARD_DATA = 'update_card_data';
export const RECEIVE_CHANNEL_SUGGEST = 'receive_channel_suggest';
export const CLOSE_CONTENT_EDIT_MODAL = 'close_edit_content_modal';
export const CLOSE_CONFIRM_CONTENT_EDIT_MODAL = 'close_confirm_edit_content_modal';
export const RECEIVE_TOPIC_SUGGEST_CONTENT = 'receive_topic_suggest_content';
export const ADD_TOPIC_TO_MODAL_CONTENT = 'add_topic_to_modal_content';
export const REMOVE_TOPIC_FROM_MODAL_CONTENT = 'remove_topic_from_modal_content';
export const RECEIVE_CHANNEL_SUGGEST_CONTENT = 'receive_channel_suggest_content';
export const ADD_CHANNEL_TO_MODAL_CONTENT = 'add_channel_to_modal_content';
export const REMOVE_CHANNEL_FROM_MODAL_CONTENT = 'remove_channel_from_modal_content';
export const SHOW_MORE_TAGS_CONTENT = 'show_more_tags_content';
export const CLOSE_TAGS_MODAL = 'close_tags_modal';
export const OPEN_HISTORY_MODAL_CONTENT = 'open_message_modal_content';
export const CLOSE_HISTORY_MODAL = 'close_history_modal';
export const CHANGE_LIMIT_CONTENTS_PER_PAGE = 'change_limit_contents_per_page';
export const RECEIVE_PAGE_CONTENTS = 'receive_page_contents';
export const EMPTY_CONTENT_CHECKED_IDS = 'empty_content_checked_ids';
export const GET_ALL_CONTENT_IMPORTS = 'get_all_content_imports';
export const GET_IMPORTED_CONTENTS = 'get_imported_contents';
export const CHANGE_LIMIT_IMPORTED_CONTENTS = 'change_limit_imported_contents';
export const OPEN_CONTENT_IMPORT_STATUS_MODAL = 'open_content_import_status_modal';

//live stream
export const OPEN_DELETE_LIVE_STREAM  = 'open_delete_live_stream';
export const CLOSE_DELETE_LIVE_STREAM = 'close_delete_live_stream';
export const OPEN_EDIT_LIVE_STREAM  = 'open_edit_live_stream';
export const CLOSE_EDIT_LIVE_STREAM = 'close_edit_live_stream';
export const RECEIVE_INIT_LIVE_STREAM = 'receive_init_live_stream';
export const RECEIVE_INIT_LIVE_STREAM_LIB = 'receive_init_live_stream_lib';
export const RECEIVE_CLONE_LIVE_STREAM = 'receive_clone_live_stream';
export const TOGGLE_CHECK_ALL_LIVE_STREAM = 'toggle_check_all_live_stream';
export const TOGGLE_LIVE_STREAM_CHECK = 'toggle_live_stream_check';
export const TOGGLE_CHECK_ALL_LIVE_STREAM_LIB = 'toggle_check_all_live_stream_lib';
export const TOGGLE_LIVE_STREAM_LIB_CHECK = 'toggle_live_stream_lib_check';
export const RECEIVE_PUBLISH_LIVE_STREAM = 'receive_publish_live_stream';
export const RECEIVE_DELETE_LIVE_STREAM = 'receive_delete_live_stream';
export const RECEIVE_EDIT_LIVE_STREAM = 'receive_edit_live_stream';
export const ADD_TOPIC_TO_MODAL = 'add_topic_to_modal';
export const REMOVE_TOPIC_FROM_MODAL = 'remove_topic_from_modal';
export const ADD_CHANNEL_TO_MODAL = 'add_channel_to_modal';
export const REMOVE_CHANNEL_FROM_MODAL = 'remove_channel_from_modal';
export const RECEIVE_PAGE_LIVE_STREAM = 'receive_page_livestreams';
export const CHANGE_LIMIT_LIVE_STREAM_PER_PAGE = 'change_limit_livestreams_per_page';
export const RECEIVE_TOPIC_SUGGEST_LIVESTREAM = 'receive_topic_suggest_livestream';
export const RECEIVE_CHANNEL_SUGGEST_LIVESTREAM = 'receive_channel_suggest_livestream';
export const EMPTY_LIVESTREAM_CHECKED_IDS = 'empty_livestream_checked_ids';
export const OPEN_VIDEO_MODAL = 'open_video_modal';
export const CLOSE_VIDEO_MODAL = 'close_video_modal';


//courses
export const RECEIVE_INIT_COURSES = 'receive_init_courses';
export const PROMOTE_COURSES = 'promote_courses';
export const UNPROMOTE_COURSES = 'unpromote_courses';
export const HIDE_COURSES = 'hide_courses';
export const UNHIDE_COURSES = 'unhide_courses';
export const TOGGLE_CHECK_ALL_COURSES = 'toggle_check_all_courses';
export const TOGGLE_COURSE = 'toggle_course';
export const RECEIVE_EDIT_COURSE = 'receive_edit_course';
export const CLOSE_EDIT_COURSE_MODAL = 'close_edit_course_modal';
export const RECEIVE_TOPIC_SUGGEST_COURSES = 'receive_topic_suggest_courses';
export const ADD_TOPIC_TO_MODAL_COURSES = 'add_topic_to_modal_courses';
export const REMOVE_TOPIC_FROM_MODAL_COURSES = 'remove_topic_from_modal_courses';
export const RECEIVE_CHANNEL_SUGGEST_COURSES = 'receive_channel_suggest_courses';
export const ADD_CHANNEL_TO_MODAL_COURSES = 'add_channel_to_modal_courses';
export const REMOVE_CHANNEL_FROM_MODAL_COURSES = 'remove_channel_from_modal_courses';
export const CHANGE_LIMIT_COURSES_PER_PAGE = 'change_limit_courses_per_page';
export const RECEIVE_NEXT_COURSES = 'receive_next_courses';
export const RECEIVE_PREVIOUS_COURSES = 'receive_previous_courses';
export const RECEIVE_PAGE_COURSES = 'receive_page_courses';
export const EMPTY_COURSE_CHECKED_IDS = 'empty_course_checked_ids';
export const EXPORT_COURSES = 'export_courses';

//channels
export const RECEIVE_INIT_CHANNELS = 'receive_init_channels';
export const TOGGLE_CHECK_ALL_CHANNELS = 'toggle_check_all_channels';
export const TOGGLE_CHANNEL_CHECK = 'toggle_channel_check';
export const PROMOTE_CHANNEL = 'promote_channel';
export const UNPROMOTE_CHANNEL = 'unpromote_channel';
export const RECEIVE_DELETE_CHANNEL = 'receive_delete_channel';
export const RECEIVE_USERS_SUGGEST_FOR_CHANNEL = 'receive_users_suggest_for_channel';
export const ADD_USER_TO_CHANNEL_MODAL = 'add_user_to_channel_modal';
export const REMOVE_USER_FROM_CHANNEL_MODAL = 'remove_user_from_channel_modal';
export const EMPTY_CHANNEL_CHECKED_IDS = 'empty_channel_checked_ids';
export const SHOW_CAROUSEL_CARD_MODAL = 'SHOW_CAROUSEL_CARD_MODAL';
export const HIDE_CAROUSEL_CARD_MODAL = 'HIDE_CAROUSEL_CARD_MODAL';
export const RECEIVE_INIT_MAPPED_ORGANIZATIONS = 'receive_init_mapped_organizations';

//global channels
export const RECEIVE_INIT_GLOBAL_CHANNELS = 'receive_init_global_channels';
export const CHANGE_GLOBAL_CHANNELS_LIMIT = 'change_global_channels_limit';
export const SHOW_ENABLE_GLOBAL_CHANNEL_MODAL = "show_enable_global_channel_modal";
export const HIDE_ENABLE_GLOBAL_CHANNEL_MODAL = "hide_enable_global_channel_modal";
export const RECEIVE_PAGE_GLOBAL_CHANNELS = "receive_page_global_channels";
export const CHANNEL_CLONED = "channel_cloned";
export const DISABLE_GLOBAL_CHANNEL = "disable_global_channel";

//shared channels
export const RECEIVE_INIT_SHARED_CHANNELS = 'receive_init_shared_channels';
export const TOGGLE_CHECK_ALL_SHARED_CHANNELS = 'toggle_check_all_shared_channels';
export const TOGGLE_SHARED_CHANNEL_CHECK = 'toggle_shared_channel_check';
export const PROMOTE_SHARED_CHANNEL = 'promote_shared_channel';
export const UNPROMOTE_SHARED_CHANNEL = 'unpromote_shared_channel';
export const SHOW_DELETE_SHARED_CHANNEL_MODAL = 'show_delete_shared_channel_modal';
export const SHOW_ADD_SHARED_CHANNEL_MODAL = 'show_shared_channel_new_modal';
export const HIDE_DELETE_SHARED_CHANNEL_MODAL = 'hide_delete_shared_channel_modal';
export const RECEIVE_DELETE_SHARED_CHANNEL = 'receive_delete_shared_channel';
export const SHOW_EDIT_SHARED_CHANNEL_MODAL = 'show_edit_shared_channel_modal';
export const HIDE_EDIT_SHARED_CHANNEL_MODAL = 'hide_edit_shared_channel_modal';
export const SET_SHARED_CHANNEL_DEFAULT_BANNER = 'set_shared_channel_default_banner';
export const DELETE_SHARED_CHANNEL_BANNER = 'delete_shared_channel_banner';
export const SET_SHARED_CHANNEL_CURRENT_BANNER = 'set_shared_channel_current_banner';
export const SET_SHARED_CHANNEL_DEFAULT_PROFILE = 'set_shared_channel_default_profile';
export const DELETE_SHARED_CHANNEL_PROFILE_SRC = 'delete_shared_channel_profile_src';
export const SET_SHARED_CHANNEL_CURRENT_PROFILE_SRC = 'set_shared_channel_current_profile_src';
export const SET_SHARED_CHANNEL_AUTO_FOLLOW = 'set_shared_channel_auto_follow';
export const UPDATE_SHARED_CHANNEL = 'update_shared_channel';
export const CHANGE_SHARED_CHANNELS_LIMIT = 'change_shared_channels_limit';
export const RECEIVE_PAGE_SHARED_CHANNELS = 'receive_page_shared_channels';
export const SHOW_SHARED_CHANNEL_SOURCES_MODAL = 'show_shared_channel_sources_modal';
export const HIDE_SHARED_CHANNEL_SOURCES_MODAL = 'hide_shared_channel_sources_modal';
export const REQUEST_UPLOAD_SHARED_CHANNEL_IMAGE = 'request_upload_shared_channel_image';
export const RECEIVE_UPLOAD_SHARED_CHANNEL_IMAGE = 'receive_upload_shared_channel_image';
export const RECEIVE_USERS_SUGGEST_FOR_SHARED_CHANNEL = 'receive_users_suggest_for_shared_channel';
export const RECEIVE_SHARED_CHANNEL_COLLABORATORS = 'receive_shared_channel_collaborators';
export const GET_SHARED_CHANNEL_SOURCE = 'get_shared_channel_source';
export const SHOW_SHARED_CHANNEL_COLLABORATORS_MODAL = 'show_shared_channel_collaborators_modal';
export const SHOW_SHARED_CHANNEL_HISTORY_MODAL = 'show_shared_channel_history_modal';
export const HIDE_SHARED_CHANNEL_HISTORY_MODAL = 'hide_shared_channel_history_modal';
export const HIDE_SHARED_CHANNEL_COLLABORATORS_MODAL = 'hide_shared_channel_collaborators_modal';
export const HIDE_SHARED_CHANNEL_FOLLOWERS_MODAL = 'hide_shared_channel_followers_modal';
export const SHARED_CHANNEL_COLLABORATORS_SEARCH = 'channel_collaborators_search';
export const SHOW_SHARED_CHANNEL_CAROUSEL_CARD_MODAL = 'show_shared_channel_caroused_card_modal';
export const HIDE_SHARED_CHANNEL_CAROUSEL_CARD_MODAL = 'hide_shared_channel_carousel_card_modal';

//query
export const REQUEST_CMS_QUERY = 'request_cms_query';
export const RECEIVE_CMS_QUERY = 'receive_cms_query';

//overlay
export const MODAL_CLOSED = 'modal_closed';
export const CLOSE_MODAL = 'close_modal';
export const CONFIRM_MODAL = 'confirm_modal';
export const CONFIRM_OFF = 'confirm_off';
export const OPEN_CONFIRM_MODAL = 'open_confirm_modal';
export const OPEN_ADD_CUSTOM_FIELD_MODAL = 'open_add_custom_field_modal';
export const OPEN_MEDIA_UPLOAD_MODAL = 'open_media_upload_modal';
export const GET_ALL_CUSTOM_FIELDS = 'get_all_custom_fields';
export const ENABLE_IN_FILTERS = 'enable_in_filters';
export const REMOVE_CUSTOM_FIELD = 'remove_custom_field';
export const CLOSE_CUSTOM_FIELD_MODAL = 'close_custom_field_modal';
export const GET_ALL_PARTNER_CENTERS = 'get_all_partner_centers';
export const OPEN_ADD_PARTNER_CENTER_MODAL = 'open_add_partner_center_modal';
export const CLOSE_PARTNER_CENTER_MODAL = 'close_partner_center_modal';
export const OPEN_EDIT_PARTNER_CENTER_MODAL = 'open_edit_partner_center_modal';
export const ADD_CUSTOM_FIELD = 'add_custom_field';

export const SHOW_ALERT = 'show_alert';
export const CLOSE_ALERT = 'close_alert';
export const OPEN_EDIT_CONTENT_MODAL = 'open_edit_content_modal';
export const OPEN_CONFIRM_EDIT_CONTENT_MODAL = 'open_confirm_edit_content_modal';
export const SHOW_MORE_TAGS = 'show_more_tags';
export const OPEN_ADD_FILTER_MODAL = 'open_add_filter_modal';
export const RECEIVE_CHANGE_CONTENT_MODAL_IMAGE = 'receive_change_content_modal_image';
export const RECEIVE_CHANGE_LIVE_STREAM_MODAL_IMAGE = 'receive_change_live_stream_modal_image';
export const OPEN_CLONE_LIVE_STREAM_MODAL = 'open_clone_live_stream_modal';
export const OPEN_EDIT_LIVE_STREAM_MODAL = 'open_edit_live_stream_modal';
export const RECEIVE_USER_SUGGEST = 'receive_user_suggest';
export const RECEIVE_CMS_ADMIN_SUGGEST = 'receive_cms_admin_suggest';
export const OPEN_EDIT_COURSE_MODAL = 'open_edit_course_modal';
export const OPEN_MESSAGE_MODAL = 'open_message_modal';
export const SHOW_WARNING = 'show_warning';
export const CLOSE_WARNING = 'close_warning';
export const OPEN_CLC_EDIT_MODAL = 'OPEN_clc_edit_modal';
export const CLOSE_CLC_EDIT_MODAL = 'close_clc_edit_modal';
export const UPDATE_CLC = 'update_clc';
export const DELETE_CLC = 'delete_clc';
export const DELETE_CLC_MODAL = 'delete_clc_modal';
export const CLOSE_DELETE_CLC_MODAL = 'close_delete_clc_modal';
export const ACTIVE_INACTIVE_CLC_MODAL = 'active_inactive_clc_modal';
export const CLOSE_ACTIVE_INACTIVE_CLC_MODAL = 'close_active_inactive_clc_modal';
export const CLOSE_DYNAMIC_GROUP_DELETE_MODAL = 'close_dynamic_delete_modal';
export const OPEN_DYNAMIC_GROUP_DELETE_MODAL = 'open_dynamic_delete_modal';
export const DELETED_DYNAMIC_WORKFLOW = 'deleted_dynamic_workflow';
export const OPEN_DYNAMIC_GROUP_VIEW_MODAL = 'open_dynamic_view_modal';
export const CLOSE_DYNAMIC_GROUP_VIEW_MODAL = 'close_dynamic_view_modal';
export const CLOSE_DYNAMIC_PREVIEW_GROUP_NAME = 'close_dynamic_preview_group_name';

/**
 *  OrgSettings
 */

//orgAuth
export const REQUEST_GET_SSO_ITEMS = 'request_get_sso_items';
export const RECEIVE_GET_SSO_ITEMS = 'receive_get_sso_items';
export const REQUEST_TOGGLE_SSO = 'request_toggle_sso';
export const RECEIVE_TOGGLE_SSO = 'receive_toggle_sso';
export const CLOSE_SSO_DETIAL = 'close_sso_detial';
export const SHOW_SSO_DETIAL = 'show_sso_detial';
export const REQUEST_SAVE_SSO_DETAIL = 'request_save_sso_detail';
export const RECEIVE_SAVE_SSO_DETAIL = 'receive_save_sso_detail';

//orgSettings
export const TOGGLE_PERMISSION = 'toggle_permission';
export const GET_PERMISSIONS = 'get_permissions';

//orgPreferences
export const GET_PREFERENCES = 'get_preferences';
export const RECEIVE_ROUTES_ITEMS = 'receive_routes_items';
export const REQUEST_UPLOAD_IMAGE = 'request_upload_image';
export const RECEIVE_UPLOAD_IMAGE = 'receive_upload_image';
export const REQUEST_UPLOAD_BANNER_IMAGE = 'request_upload_banner_image';
export const RECEIVE_UPLOAD_BANNER_IMAGE = 'receive_upload_banner_image';
export const VALIDATE_HOSTNAME = 'validate_hostname';
export const SAVE_SETTINGS = 'save_settings';

// LXMediaHub configs
export const UPDATE_LX_MEDIA_HUB_CONFIG = 'update_lx_media_hub_config';
export const UPDATE_LX_MEDIA_HUB_SOURCES = 'update_lx_media_hub_sources';

/**
 *  GET
 */

//GET Finalists
export const PREV_MONTH = 'prev_month';
export const NEXT_MONTH = 'next_month';
export const END_OF_EVENT = 'end_of_event';
export const FINALIST_WINNERS = 'finalist_winners';


// GROUPS MANAGER
export const RECEIVE_INIT_GROUPS = 'receive_init_groups';
export const GROUPS_OPEN_ADD_MODAL = 'groups_open_add_modal';
export const GROUPS_OPEN_EDIT_MODAL = 'groups_open_edit_modal';
export const GROUPS_OPEN_SEND_MODAL = 'groups_open_send_modal';
export const GROUPS_OPEN_DELETE_MODAL = 'groups_open_delete_modal';
export const GROUPS_SEND_MESSAGE = 'groups_send_message';
export const GROUPS_DELETE_GROUP = 'groups_delete_group';
export const GROUPS_CLOSE_GROUP_MODAL  = 'groups_close_group_modal';
export const GROUPS_CLOSE_DELETE_MODAL = 'groups_close_delete_modal';
export const GROUPS_CLOSE_SEND_MODAL   = 'groups_close_close_modal';
export const CHANGE_LIMIT_GROUPS_PER_PAGE = 'change_limit_groups_per_page';
export const GROUPS_CLOSE_MEMBERS_MODAL = 'groups_close_members_modal';
export const GROUPS_SHOW_MEMBERS_MODAL = 'groups_show_members_modal';
export const DELETE_GROUP_USER = 'delete_group_user';
export const DELETE_GROUP_USERS = 'delete_group_users';
export const ADD_GROUP_USER = 'add_group_user';
export const GROUPS_SEARCH_MEMBERS = 'groups_search_members';
export const GET_CHANNELS_OF_GROUP = 'get_channels_of_group';
export const GROUPS_OPEN_USERS_REMOVE_MODAL = 'groups_open_users_remove_modal';
export const GROUPS_CLOSE_USERS_REMOVE_MODAL = 'groups_close_users_remove_modal';

// ECL
export const RECEIVE_ECL_CHANNELS = 'receive_ecl_channels';
export const ECL_CONFIG_SAVED = 'ecl_config_saved';
export const RECEIVE_ECL_PROVIDERS = 'receive_ecl_providers';
export const RECEIVE_ECL_SOURCES = 'receive_ecl_sources';
export const OPEN_PROVIDER_MODAL = 'open_provider_modal';
export const OPEN_ARCHIVE_PROVIDER_MODAL = 'open_archive_provider_modal';
export const OPEN_SOURCE_MODAL = 'open_source_modal';
export const OPEN_SOURCE_REPORT_MODAL = 'open_source_report_modal';
export const CLOSE_SOURCE_REPORT_MODAL = 'close_source_report_modal';
export const OPEN_CONTENT_COUNT_MODAL = 'open_content_count_modal';
export const CLOSE_CONTENT_COUNT_MODAL = 'close_content_count_modal'
export const RECEIVE_CHANNEL_SOURCES = 'receive_channel_sources';
export const OPEN_DELETE_SOURCE_MODAL = 'open_delete_source_modal';
export const ECL_SOURCE_DELETED = 'ecl_source_deleted';
export const OPEN_BOX_PROVIDER_MODAL = 'open_box_provider_modal';
export const OPEN_SHAREPOINT_TOKEN_BASED_PROVIDER_MODAL = 'open_sharepoint_token_based_provider_modal';

// AUTHENTICATION
export const SSO_GET_LIST = 'sso_get_list';
export const SSO_ENABLE   = 'sso_enable';
export const SSO_DISABLE  = 'sso_disable';
export const HIDE_SAML_MODAL  = 'hide_saml_modal';
export const OPEN_SAML_MODAL  = 'open_saml_modal';
export const HIDE_ORGOAUTH_MODAL  = 'hide_orgoauth_modal';
export const OPEN_ORGOAUTH_MODAL  = 'open_orgoauth_modal';
export const OPEN_LXP_OAUTH_MODAL = 'open_lxp_oauth_modal';
export const CLOSE_LXP_OAUTH_MODAL = 'close_lxp_oauth_modal'

// Sources Credentials
export const SHOW_SOURCES_CREDENTIAL_INFO_MODAL = 'show_source_credential_info_modal';
export const HIDE_SOURCES_CREDENTIAL_INFO_MODAL = 'hide_source_credential_info_modal';
export const SHOW_SOURCES_CREDENTIAL_EDIT_MODAL = 'show_source_credential_edit_modal';
export const HIDE_SOURCES_CREDENTIAL_EDIT_MODAL = 'hide_source_credential_edit_modal';
export const ADD_SOURCES_CREDENTIAL_MODAL = 'add_source_credential_modal';
export const HIDE_SOURCES_CREDENTIAL_MODAL = 'hide_source_credential_modal';
export const RECEIVE_SOURCES_CREDENTIALS = 'receive_sources_credentials';

// integrations
export const RECEIVE_INIT_XAPI_CREDENTIALS = 'receive_init_xapi_credentials';
export const CREATE_XAPI_CREDENTIALS = 'create_xapi_credentials';
export const SHOW_EDIT_XAPI_CREDENTIALS = 'show_edit_xapi_credentials';
export const RECEIVE_INIT_OAUTH2_CREDENTIALS = 'receive_init_oauth2_credentials';
export const SHOW_CREATE_OAUTH2_CREDENTIALS = 'show_create_oauth2_credentials';
export const EDIT_XAPI_CREDENTIALS = 'edit_xapi_credentials';
export const HIDE_XAPI_MODAL = 'hide_xapi_modal';
export const HIDE_OAUTH2_MODAL = 'hide_oauth2_modal';
export const OPEN_CREATE_MODAL = 'open_create_modal';
export const ACTIVATE_XAPI = 'activate_xapi';
export const DEACTIVATE_XAPI = 'deactivate_xapi';
export const ECL_ACCESS_UPDATED = 'ecl_access_updated';
export const RECEIVE_LMS_SOURCE_TYPES = 'receive_lms_source_types';
export const RECEIVE_LMS_SOURCES = 'receive_lms_sources';
export const OPEN_DELETE_LMS_SOURCE_MODAL = 'open_delete_lms_source_modal';
export const OPEN_LMS_PROVIDER_MODAL = 'open_lms_provider_modal';
export const LMS_SOURCE_DELETED = 'lms_source_deleted';
export const LMS_CONFIG_SAVED = 'lms_config_saved';
export const OPEN_HRMS_JOB_USERS_MODAL = 'open_hrms_job_users_modal';
export const OPEN_OAUTH2_MODAL = 'open_oauth2_modal';
export const OPEN_LMS_ARCHIVE_MODAL = 'open_lms_archive_modal';
export const OPEN_SOURCE_EXCEPTION_LOGS_MODAL = 'open_source_exception_logs_modal';
export const OPEN_LMS_CONFIG_MODAL = 'open_lms_config_modal';
export const CLOSE_LMS_CONFIG_MODAL = 'close_lms_config_modal';
export const RECEIVE_INIT_LMS_CONFIGS = 'receive_init_lms_configs';


export const LYNDA_STATUS  = 'lynda_status';
export const BOX_STATUS  = 'box_status';
export const GETABSTRACT_STATUS  = 'Getabstract_status';
export const INIT_PROVIDERS_LIST  = 'init_providers_list';
export const SAVE_SAML_IDP_PARAMETERS  = 'save_saml_idp_parameters';
export const SAVE_ORGOAUTH_IDP_PARAMETERS  = 'save_orgoauth_idp_parameters';
export const RECEIVE_CREDENTIALS  = 'receive_credentials';

//assignments
export const RECEIVE_INIT_ASSIGNMENTS  = 'receive_init_assignments';
export const SHOW_ASSIGNMENTS_USERS_MODAL  = 'show_assignments_users_modal';
export const HIDE_ASSIGNMENTS_USERS_MODAL  = 'hide_assignments_users_modal';
export const SHOW_CONFIRMATION_MODAL  = 'show_confirmation_modal';
export const HIDE_CONFIRMATION_MODAL  = 'hide_confirmation_modal';
export const SEND_REMINDER  = 'send_reminder';


// import page
export const ADD_CHANNEL_TO_INPUT = 'add_channel_to_input';
export const ADD_GROUP_TO_INPUT = 'add_group_to_input';
export const REMOVE_CHANNEL_FROM_INPUT = 'remove_channel_from_input';
export const REMOVE_GROUP_FROM_INPUT = 'remove_group_from_input';
export const RECEIVE_CHANNEL_SUGGEST_IMPORT_PAGE = 'receive_channel_suggest_import_page';
export const RECEIVE_GROUP_SUGGEST_IMPORT_PAGE = 'receive_group_suggest_import_page';
export const OPEN_IMPORT_PREVIEW  = 'open_import_preview';
export const CLOSE_IMPORT_PREVIEW = 'close_import_preview';
export const OPEN_ADD_COLLAB_CONFIRM_MODAL  = 'open_add_collab_confirm_modal';
export const CLEAR_IMPORT_PAGE = 'clear_import_page';
export const SHOW_MESSAGE_SUCCESS = 'show_message_success';
export const CLOSE_MESSAGE_SUCCESS = 'close_message_success';
export const ADD_EMAIL_TO_INPUT = 'add_email_to_input';
export const RESET_STATE = 'reset_state';


//CLC Actions
export const OPEN_CLC_CONFIRMATION = 'open_clc_confirmation';
export const SUCESS_CLC_UPDATE = 'sucess_clc_update';
export const FAILURE_CLC_UPDATE = 'failure_clc_update';

// Announcements
export const RECEIVE_ANNOUNCEMENTS = 'receive_announcements';

//Sociative Taxonomy
export const FETCH_SOCIATIVE_TAXONOMY = 'fetch_sociative_taxonomy';


//Report Content Actions
export const RECEIVE_INIT_REPORTED_CARDS = 'receive_init_reported_cards';
export const RECEIVE_INIT_TRASHED_CARDS = 'receive_init_trashed_cards';
export const TRASH_A_CARD = 'trash_a_card';
export const UNREPORT_A_CARD = 'unreport_a_card';
export const UNREPORT_A_CARD_FROM_TRASH = 'unreport_a_card_from_trash';

//Report Comment Actions
export const RECEIVE_INIT_REPORTED_COMMENTS = 'receive_init_reported_comments';
export const RECEIVE_INIT_TRASHED_COMMENTS = 'receive_init_trashed_comments';
export const TRASH_A_COMMENT = 'trash_a_comment';
export const UNREPORT_A_COMMENT = 'unreport_a_comment';
export const UNREPORT_A_COMMENT_FROM_TRASH = 'unreport_a_comment_from_trash';

//Email Templates
export const RECEIVE_INIT_TEMPLATES = 'receive_init_templates';
export const RECEIVE_TEMPLATES = 'receive_templates';
export const UPDATE_TEMPLATES = 'update_templates';
export const UPDATE_TEMPLATE = 'update_template';
export const DELETE_TEMPLATE = 'delete_template';
export const ACTIVATE_TEMPLATE = 'activate_template';
export const DEACTIVATE_TEMPLATE = 'deactivate_template';
export const RECEIVE_INIT_MANAGE_SENDERS = 'receive_init_manage_senders';
export const SENDER_EDIT_MODAL = 'sender_edit_modal';
export const ADD_SENDERS_EMAIL = "add_senders_email";
export const REMOVE_SENDERS_EMAIL = "remove_senders_email";
export const UPDATE_SENDERS_EMAIL = "update_senders_email";
export const HIDE_SENDER_EMAIL_DELETE_MODAL = "hide_sender_email_delete_modal";
export const SHOW_SENDER_EMAIL_DELETE_MODAL = "show_sender_email_delete_modal";

// Domo Creds
export const GET_DOMO_CREDS = 'get_domo_creds'
export const ADD_DOMO_CREDS = "add_domo_creds";
export const DOMO_CREDS_EDIT_MODAL = 'domo_creds_edit_modal';
export const UPDATE_DOMO_CREDS = "update_domo_creds";
export const REMOVE_DOMO_CREDS = "remove_domo_creds";
export const HIDE_DOMO_CREDS_DELETE_MODAL = "hide_domo_creds_delete_modal";
export const SHOW_DOMO_CREDS_DELETE_MODAL = "show_domo_creds_delete_modal";

// Embed Domo
export const GET_EMBED_DOMO = 'get_embed_domo'
export const ADD_EMBED_DOMO = "add_embed_domo";
export const EMBED_DOMO_EDIT_MODAL = 'embed_domo_edit_modal';
export const UPDATE_EMBED_DOMO = "update_embed_domo";
export const REMOVE_EMBED_DOMO = "remove_embed_domo";
export const HIDE_EMBED_DOMO_DELETE_MODAL = "hide_embed_domo_delete_modal";
export const SHOW_EMBED_DOMO_DELETE_MODAL = "show_embed_domo_delete_modal";

// User Import Statuses
export const GET_ALL_USER_IMPORTS = 'get_all_user_imports';
export const GET_IMPORTED_USERS = 'get_imported_users';
export const CHANGE_LIMIT_IMPORTED_USERS = 'change_limit_imported_users';
export const OPEN_USER_IMPORT_STATUS_MODAL = 'open_user_import_status_modal';

// Workflow
export const OPEN_WORKFLOW_STATUS_MODAL = 'open_workflow_status_modal'

// circle
export const CIRCLE_ADDED_TOGGLE = 'CIRCLE_ADDED_TOGGLE';

//Rendering wso2 credentials
export const RECEIVE_WSO2_CREDENTIALS  = 'receive_wso2_credentials';
export const CREATE_WSO2_CREDENTIAL_MODAL = 'create_wso2_credential_modal';
export const EDIT_WSO2_CREDENTIAL_MODAL = 'edit_wso2_credential_modal';
export const RECEIVE_SUBSCRIPTIONS = 'receive_subscriptions';
export const FETCH_APPLICATION = 'fetch_application';
export const RECEIVE_THROTTLING = 'receive_throttling';

// Kong credentials action types
export const KONG_CREDENTIALS_LOADING = 'KONG_CREDENTIALS_LOADING';
export const KONG_CREDENTIALS_LOADED = 'KONG_CREDENTIALS_LOADED';
export const KONG_CREDENTIALS_LOAD_ERROR = 'KONG_CREDENTIALS_LOAD_ERROR';
export const KONG_CREDENTIAL_CREATED = 'KONG_CREDENTIAL_CREATED';
export const KONG_CREDENTIAL_DELETED = 'KONG_CREDENTIAL_DELETED';
export const KONG_CREDENTIAL_UPDATED = 'KONG_CREDENTIAL_UPDATED';
export const SHOW_MODAL = 'SHOW_MODAL';