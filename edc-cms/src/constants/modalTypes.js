export const ADD_NEW_SOURCE = 'add_new_source';
export const EDIT_SOURCE = 'edit_source';
export const CONFIRM = 'confirm';
export const JOBS_ACTION = 'jobs_action';
export const EDIT_CONTENT = 'edit_content';
export const TAGS = 'tags';
export const ADD_FILTER = 'add_filter';
export const CLONE_LIVE_STREAM = 'clone_live_stream';
export const EDIT_LIVE_STREAM = 'edit_live_stream';
export const CLONE_PATHWAY = 'clone_pathway';
export const EDIT_PATHWAY = 'edit_pathway';
export const EDIT_COURSE = 'edit_course';
export const MESSAGE = 'message';
export const PROVIDER_MODAL = 'provider_modal';
export const ARCHIVE_PROVIDER_MODAL = 'archive_provider_modal';
export const BOX_PROVIDER_MODAL = 'box_provider_modal';
export const SHAREPOINT_TOKEN_BASED_PROVIDER_MODAL = 'sharepoint_token_based_provider_modal';
export const SOURCE_MODAL = 'source_modal';
export const DELETE_SOURCE = 'delete_source';
export const ADD_COLLAB_CONFIRM = 'add_collab_confirm';
export const DELETE_LMS_SOURCE = 'delete_lms_source';
export const LMS_PROVIDER_MODAL = 'lms_provider_modal';
export const CUSTOM_FIELD_MODAL = 'custom_field_modal';
export const PARTNER_CENTER_MODAL = 'partner_center_modal';
export const OPEN_WORKFLOW_STATUS = 'open_workflow_status';
export const SOURCE_REPORT_MODAL = 'source_report_modal';
export const CONTENT_COUNT_MODAL = 'content_count_modal';
export const HRMS_USERS_MODAL = 'hrms_users_modal';
export const OAUTH2_MODAL = 'oauth2_modal';
export const LMS_ARCHIVE_MODAL = 'lms_archive_modal';
export const OPEN_CLC_EDIT_MODAL = 'open_clc_edit_modal';
export const OPEN_DELETE_CLC_MODAL = 'open_delete_clc_modal';
export const OPEN_ACTIVE_INACTIVE_CLC_MODAL = 'open_active_inactive_clc_modal';
export const SOURCE_EXCEPTION_LOGS_MODAL = 'source_exception_logs_modal';
export const OPEN_DYNAMIC_GROUP_DELETE_MODAL = 'open_dynamic_group_delete_modal';
export const OPEN_DYNAMIC_GROUP_VIEW_MODAL = 'open_dynamic_group_view_modal';
export const OPEN_DYNAMIC_PREVIEW_GROUP_NAME_MODAL = 'open_dynamic_preview_group_name_modal';
export const OPEN_USER_IMPORT_STATUS = 'open_user_import_status';
export const OPEN_ADD_MKP_ADMIN_USER_MODAL = 'open_add_mkp_admin_user_modal';
export const OPEN_DELETE_MKP_ADMIN_USER_MODAL = 'open_delete_mkp_admin_user_modal';
export const OPEN_LMS_CONFIG_MODAL = 'open_lms_config_modal';
export const CREATE_WSO2_CREDENTIAL_MODAL = 'create_wso2_credential_modal';
export const EDIT_WSO2_CREDENTIAL_MODAL = 'edit_wso2_credential_modal';
export const KONG_CREDENTIAL_CREATE = 'KONG_CREDENTIAL_CREATE';
export const KONG_CREDENTIAL_EDIT = 'KONG_CREDENTIAL_EDIT';
