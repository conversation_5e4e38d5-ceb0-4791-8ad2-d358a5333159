import * as actionTypes from '../constants/actionTypes';

const initialState = {
  kongCredentials: {},
  loaded: false,
  loading: false,
  error: null
};

export default function kongCredentialsReducer(state = initialState, action) {
  switch (action.type) {
    case actionTypes.KONG_CREDENTIALS_LOADING:
      return {
        ...state,
        loading: true,
        loaded: false,
        error: null
      };
    case actionTypes.KONG_CREDENTIALS_LOADED:
      return {
        ...state,
        kongCredentials: action.kongCredentials,
        loading: false,
        loaded: true,
        error: null
      };
    case actionTypes.KONG_CREDENTIALS_LOAD_ERROR:
      return {
        ...state,
        loading: false,
        loaded: false,
        error: action.error
      };
    case actionTypes.KONG_CREDENTIAL_CREATED:
      return {
        ...state,
        lastCreated: action.credential
      };
    case actionTypes.KONG_CREDENTIAL_DELETED:
      return {
        ...state,
        lastDeleted: action.id
      };
    case actionTypes.KONG_CREDENTIAL_UPDATED:
      return {
        ...state,
        lastUpdated: action.credential
      };
    default:
      return state;
  }
}