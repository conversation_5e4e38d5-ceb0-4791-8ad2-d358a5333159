import * as actionTypes from '../constants/actionTypes.js';
import * as modalTypes  from '../constants/modalTypes.js';

export default function overlayReducer(state = {modal: {isOpen: false}, alert: {isShow: false}, warning: {isShow: false}, updatedClc: {}, deletedClc: {}, deletedWorkflow: {}, deletedMkpAdminUser: {}, addedMkpAdminUser: {}}, action) {
    let newState, newModal, newModalData, newAlert, newTopics, newChannels;
    switch (action.type) {
        //sources
        case actionTypes.OPEN_ADD_NEW_SOURCE_MODAL:
            newModalData = Object.assign({}, state.modal.data,{channels:[]});
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.ADD_NEW_SOURCE,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.REQUEST_DELETE_SOURCE:
        case actionTypes.CLOSE_MODAL:
            newModal = {isOpen: false};
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CONFIRM_MODAL:
            newModal = {isOpen: false, answer: true};
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.JOBS_ACTION_MODAL:
            newModal = Object.assign({}, state.modal,
                {
                    isOpen: true,
                    type: modalTypes.JOBS_ACTION,
                    data: action.data
                }
            );
            return Object.assign({}, state, {modal: newModal});
        case actionTypes.CONFIRM_OFF:
            newModal = {answer: false};
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_EDIT_SOURCE_MODAL:
            newModal = Object.assign({}, state.modal,
                {
                    isOpen: true,
                    type: modalTypes.EDIT_SOURCE,
                    data: action.source
                }
            );
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.RECEIVE_SOURCE_URL_VALIDATION:
            newModalData = Object.assign({}, state.modal.data, {isUrlValid: action.isValid, error: undefined});
            newModal = Object.assign({}, state.modal, {data: newModalData});
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.ERROR_SAVE_SOURCE:
            newModalData = Object.assign({}, state.modal.data, {error: action.error, isUrlValid: undefined});
            newModal = Object.assign({}, state.modal, {data: newModalData});
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_CONFIRM_MODAL:
            newModal = Object.assign({}, state.modal,
                {
                    isOpen: true,
                    type: modalTypes.CONFIRM,
                    data: action.data
                }
            );
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_MEDIA_UPLOAD_MODAL:
            newModal = Object.assign({}, state.modal,
                {
                    isOpen: true,
                    type: modalTypes.MEDIA_UPLOAD,
                    data: action.data
                }
            );
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_MESSAGE_MODAL:
            newModal = Object.assign({}, state.modal,
                {
                    isOpen:true,
                    type: modalTypes.MESSAGE,
                    data: action.data
                }
            );
            return Object.assign({}, state, {modal: newModal});
            break;
        //contents
        case actionTypes.OPEN_EDIT_CONTENT_MODAL:
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.EDIT_CONTENT,
                data: action.content
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.SHOW_ALERT:
            newAlert = {isShow: true, type: 'alert', message: action.message, page: action.page, infoIcon: action.infoIcon};
            return Object.assign({}, state, {alert: newAlert});
            break;
        case actionTypes.CLOSE_ALERT:
            newAlert = {isShow: false};
            return Object.assign({}, state, {alert: newAlert});
            break;
        case actionTypes.SHOW_WARNING:
            newState = {
                isShow: true,
                message: action.message,
                header : action.header
            };
            return Object.assign({}, state, {warning: newState});
            break;
        case actionTypes.CLOSE_WARNING:
            newState = {isShow: false};
            return Object.assign({}, state, {warning: newState});
            break;
        case actionTypes.RECEIVE_TOPIC_SUGGEST:
            newModalData = Object.assign({}, state.modal.data, {topicSuggestions: action.suggestions});
            newModal = Object.assign({}, state.modal, {data: newModalData});
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.RECEIVE_CHANNEL_SUGGEST:
            newModalData = Object.assign({}, state.modal.data, {channelSuggestions: action.suggestions});
            newModal = Object.assign({}, state.modal, {data: newModalData});
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_ADD_FILTER_MODAL:
            newModalData = {
                currentQuery: action.currentQuery,
                filterType: action.filterType,
                topics: [],
                channels: [],
                channelSuggestions: [],
                tabName: action.tabName
            };
            newModal = {
                isOpen: true,
                type: modalTypes.ADD_FILTER,
                data: newModalData
            };
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_CLONE_LIVE_STREAM_MODAL:
            newModalData = Object.assign({}, action.liveStream, {
                channels:[]
            });
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.CLONE_LIVE_STREAM,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_EDIT_LIVE_STREAM_MODAL:
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.EDIT_LIVE_STREAM,
                data: action.liveStream
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_EDIT_COURSE_MODAL:
            newModalData = Object.assign(action.course,{channelSuggestions:[]});
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.EDIT_COURSE,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_PROVIDER_MODAL:
            newModalData = Object.assign(action.provider);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.PROVIDER_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_ARCHIVE_PROVIDER_MODAL:
            newModalData = Object.assign(action.provider);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.ARCHIVE_PROVIDER_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_LMS_PROVIDER_MODAL:
            let actionObj = {action: action.action};
            newModalData = Object.assign(action.provider);
            newModalData = Object.assign(newModalData, actionObj);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.LMS_PROVIDER_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_SOURCE_MODAL:
            newModalData = Object.assign(action.source);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.SOURCE_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_SOURCE_REPORT_MODAL:
            newModalData = Object.assign(action.source);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.SOURCE_REPORT_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CLOSE_SOURCE_REPORT_MODAL:
            newModal = Object.assign({}, state.modal, {
                isOpen: false,
            });
            return Object.assign({}, state, {modal: newModal});
        case actionTypes.OPEN_CONTENT_COUNT_MODAL:
            newModalData = Object.assign(action.source);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.CONTENT_COUNT_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_DELETE_SOURCE_MODAL:
            newModalData = Object.assign(action.source);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.DELETE_SOURCE,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_DELETE_LMS_SOURCE_MODAL:
            newModalData = Object.assign(action.source);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.DELETE_LMS_SOURCE,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_ADD_COLLAB_CONFIRM_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
            isOpen: true,
            type: modalTypes.ADD_COLLAB_CONFIRM,
            data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_ADD_CUSTOM_FIELD_MODAL:
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.CUSTOM_FIELD_MODAL,
                data: action.existNames
            });
            return Object.assign({}, state, { modal: newModal });
            break;
        case actionTypes.CLOSE_CUSTOM_FIELD_MODAL:
            newModal = { isOpen: false };
            return Object.assign({}, state, { modal: newModal });
            break;
        case actionTypes.OPEN_BOX_PROVIDER_MODAL:
            newModalData = Object.assign(action.provider);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.BOX_PROVIDER_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
         case actionTypes.OPEN_SHAREPOINT_TOKEN_BASED_PROVIDER_MODAL:
            newModalData = Object.assign(action.provider);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.SHAREPOINT_TOKEN_BASED_PROVIDER_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_ADD_PARTNER_CENTER_MODAL:
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.PARTNER_CENTER_MODAL
            });
            return Object.assign({}, state, { modal: newModal });
            break;
        case actionTypes.CLOSE_PARTNER_CENTER_MODAL:
            newModal = { isOpen: false };
            return Object.assign({}, state, { modal: newModal });
            break;
        case actionTypes.OPEN_EDIT_PARTNER_CENTER_MODAL:
            newModalData = Object.assign(action.partner_center);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.PARTNER_CENTER_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, { modal: newModal });
            break;
        case actionTypes.OPEN_USER_IMPORT_STATUS_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
            isOpen: true,
            type: modalTypes.OPEN_USER_IMPORT_STATUS,
            data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_WORKFLOW_STATUS_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
            isOpen: true,
            type: modalTypes.OPEN_WORKFLOW_STATUS,
            data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_HRMS_JOB_USERS_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.HRMS_USERS_MODAL,
                data: newModalData,
                jobId: action.jobId
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_OAUTH2_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OAUTH2_MODAL,
                data: newModalData,
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_LMS_ARCHIVE_MODAL:
            newModalData = Object.assign(action.provider);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.LMS_ARCHIVE_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_CLC_EDIT_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_CLC_EDIT_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_SOURCE_EXCEPTION_LOGS_MODAL:
            newModalData = Object.assign(action.provider);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.SOURCE_EXCEPTION_LOGS_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CLOSE_CLC_EDIT_MODAL:
            newModal = {isOpen: false};
            return Object.assign({}, state, {modal: newModal, updatedClc: {}});
            break;
        case actionTypes.UPDATE_CLC:
            newModalData = Object.assign(action.data);
            return Object.assign({}, state, {updatedClc: newModalData});
            break;
        case actionTypes.DELETE_CLC:
            newModalData = Object.assign(action.data);
            return Object.assign({}, state, {deletedClc: newModalData});
            break;
        case actionTypes.DELETE_CLC_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_DELETE_CLC_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CLOSE_DELETE_CLC_MODAL:
            newModal = {isOpen: false};
            return Object.assign({}, state, {modal: newModal, deletedClc: {}});
            break;
        case actionTypes.ACTIVE_INACTIVE_CLC_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_ACTIVE_INACTIVE_CLC_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CLOSE_ACTIVE_INACTIVE_CLC_MODAL:
            newModal = {isOpen: false};
            return Object.assign({}, state, {modal: newModal, updatedClc: {}});
            break;
        case actionTypes.OPEN_DYNAMIC_GROUP_DELETE_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_DYNAMIC_GROUP_DELETE_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CLOSE_DYNAMIC_GROUP_DELETE_MODAL:
            newModal = {isOpen: false};
            return Object.assign({}, state, {modal: newModal, deletedWorkflow: {}});
            break;
        case actionTypes.DELETED_DYNAMIC_WORKFLOW:
            newModalData = Object.assign(action.data);
            return Object.assign({}, state, {deletedWorkflow: newModalData});
            break;
        case actionTypes.OPEN_DYNAMIC_GROUP_VIEW_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_DYNAMIC_GROUP_VIEW_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CLOSE_DYNAMIC_GROUP_VIEW_MODAL:
            newModal = {isOpen: false};
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CLOSE_DYNAMIC_PREVIEW_GROUP_NAME:
            newModal = {isOpen: false};
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.SHOW_DELETE_MKP_USER:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_DELETE_MKP_ADMIN_USER_MODAL,
                data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.SHOW_ADD_MKP_ADMIN_USER:
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_ADD_MKP_ADMIN_USER_MODAL,
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.HIDE_ADD_MKP_ADMIN_USER:
            newModal = { isOpen: false }
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.HIDE_DELETE_MKP_USER:
            newModal = { isOpen: false }
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.DELETED_MKP_ADMIN_USER:
            newModalData = Object.assign(action.data);
            return Object.assign({}, state, {deletedMkpAdminUser: newModalData});
            break;
        case actionTypes.ADDED_MKP_ADMIN_USER:
            newModalData = Object.assign(action.data);
            return Object.assign({}, state, {addedMkpAdminUser: newModalData});
            break;
        case actionTypes.OPEN_LMS_CONFIG_MODAL:
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.OPEN_LMS_CONFIG_MODAL,
                data: action.data
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.CREATE_WSO2_CREDENTIAL_MODAL:
            newModalData = Object.assign(action);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.CREATE_WSO2_CREDENTIAL_MODAL,
                data: newModalData,
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.EDIT_WSO2_CREDENTIAL_MODAL:
            newModalData = Object.assign(action.application);
            newModal = Object.assign({}, state.modal, {
                isOpen: true,
                type: modalTypes.EDIT_WSO2_CREDENTIAL_MODAL,
                data: newModalData,
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.OPEN_CONTENT_IMPORT_STATUS_MODAL:
            newModalData = Object.assign(action.data);
            newModal = Object.assign({}, state.modal, {
            isOpen: true,
            type: modalTypes.OPEN_CONTENT_IMPORT_STATUS,
            data: newModalData
            });
            return Object.assign({}, state, {modal: newModal});
            break;
        case actionTypes.SHOW_MODAL:
            return {
                ...state,
                modal: {
                    isOpen: true,
                    type: action.modalType,
                    modalProps: action.modalProps
                }
            };
        default :
           return state;
           break;
    }
};

