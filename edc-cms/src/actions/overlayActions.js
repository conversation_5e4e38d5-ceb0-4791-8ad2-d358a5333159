import * as actionTypes from '../constants/actionTypes';
import * as modalTypes from '../constants/modalTypes';
import * as usersSDK from 'edc-web-sdk/requests/users';
import * as usersV2SDK from 'edc-web-sdk/requests/users.v2';
import * as channelsSDK from 'edc-web-sdk/requests/channels';
import * as channelsV2SDK from 'edc-web-sdk/requests/channels.v2';
import * as topicsSDK from 'edc-web-sdk/requests/topics';
import imageUpload from 'edc-web-sdk/requests/imageUpload';

const UGC = 'ugc';

// Modal
export function closeModal() {
    return {
        type: actionTypes.CLOSE_MODAL
    }
}

export function confirmModal() {
    return {
        type: actionTypes.CONFIRM_MODAL
    }
}

export function confirmOFF() {
    return {
        type: actionTypes.CONFIRM_OFF
    }
}

export function openAddNewModal() {
    return {
        type: actionTypes.OPEN_ADD_NEW_SOURCE_MODAL
    }
}

export function openEditSourceModal(source) {
    return {
        type: actionTypes.OPEN_EDIT_SOURCE_MODAL,
        source
    }
}

export function openConfirmModal(data) {
    return {
        type: actionTypes.OPEN_CONFIRM_MODAL,
        data
    }
}

export function openMediaUploadModal(data) {
  return {
      type: actionTypes.OPEN_MEDIA_UPLOAD_MODAL,
      data
  }
}

export function openJobsActionModal(data) {
  return {
    type: actionTypes.JOBS_ACTION_MODAL,
    data
  }
}

export function openAddCustomFieldModal(existNames) {
  return {
    type: actionTypes.OPEN_ADD_CUSTOM_FIELD_MODAL,
    existNames
  }
}

export function closeCustomFieldModal() {
  return dispatch => {
    dispatch({
      type: actionTypes.CLOSE_CUSTOM_FIELD_MODAL,
    });
  }
}

export function openEditContentModal(content) {
    content = Object.assign({}, content, {topicSuggestions: [], channelSuggestions: []});
    return {
        type: actionTypes.OPEN_EDIT_CONTENT_MODAL,
        content
    }
}

export function addTopicToModal(topic) {
    return {
        type: actionTypes.ADD_TOPIC_TO_MODAL,
        topic
    }
}


export function removeTopicFromModal(index) {
    return {
        type: actionTypes.REMOVE_TOPIC_FROM_MODAL,
        index
    }
}

export function addChannelToModal(channel) {
    return {
        type: actionTypes.ADD_CHANNEL_TO_MODAL,
        channel
    }
}

export function removeChannelFromModal(index) {
    return {
        type: actionTypes.REMOVE_CHANNEL_FROM_MODAL,
        index
    }
}

export function getTopicSuggest(queryStr, limit, isECR) {
    return dispatch => {
        if (isECR) {
            topicsSDK.getECRTopicSuggest(queryStr, limit).then((suggestions)=> {
                dispatch({
                    type: actionTypes.RECEIVE_TOPIC_SUGGEST,
                    suggestions
                });
            })
        } else {
            topicsSDK.getTopicSuggest(queryStr, limit).then((suggestions)=> {
                dispatch({
                    type: actionTypes.RECEIVE_TOPIC_SUGGEST,
                    suggestions
                });
            })
        }
    }
}

export function getChannelSuggest(queryStr, limit, isECR) {
    return dispatch => {
        if (isECR) {
            channelsSDK.getECRChannelSuggest(queryStr, limit).then((suggestions)=> {
                dispatch({
                    type: actionTypes.RECEIVE_CHANNEL_SUGGEST,
                    suggestions
                });
            })
        } else {
            channelsV2SDK.getChannelSuggest(queryStr, limit).then((suggestions)=> {
                dispatch({
                    type: actionTypes.RECEIVE_CHANNEL_SUGGEST,
                    suggestions
                });
            })
        }
    }
}

export function getCreatorSuggest(queryStr,creatorType) {
    return dispatch => {
        if(creatorType === UGC){
            usersSDK.getUserSuggest(queryStr).then(suggestions=>{
                dispatch({
                    type:actionTypes.RECEIVE_USER_SUGGEST,
                    suggestions
                })
            })
        }else{
            usersSDK.getCMSAdminSuggest(queryStr).then(suggestions =>{
                dispatch({
                    type:actionTypes.RECEIVE_CMS_ADMIN_SUGGEST,
                    suggestions
                })
            })
        }
    }
}

export function openCloneLiveStreamModal(liveStream) {
    return {
        type: actionTypes.OPEN_CLONE_LIVE_STREAM_MODAL,
        liveStream
    }
}

export function openEditLiveStreamModal(liveStream) {
    return {
        type: actionTypes.OPEN_EDIT_LIVE_STREAM_MODAL,
        liveStream
    }
}



//Alert
export function showAlert(message, page) {
    return {
        type: actionTypes.SHOW_ALERT,
        message,
        page
    }
}

export function closeAlert() {
    return {
        type: actionTypes.CLOSE_ALERT
    }
}

export function showMoreTags(tags, title) {
    return ({
        type: actionTypes.SHOW_MORE_TAGS,
        tags,
        title
    })
}

export function openAddFilterModal(tabName, filterType, currentQuery) {
    return ({
        type: actionTypes.OPEN_ADD_FILTER_MODAL,
        filterType,
        currentQuery,
        tabName
    });
}


export function openEditCourseModal(course){
    return {
        type:actionTypes.OPEN_EDIT_COURSE_MODAL,
        course
    }
}

export function openMessageModal(data){
    return {
        type:actionTypes.OPEN_MESSAGE_MODAL,
        data
    }
}
//Warning
export function showWarning(message, header) {
    return {
        type: actionTypes.SHOW_WARNING,
        header,
        message
    }
}

export function closeWarning() {
    return {
        type: actionTypes.CLOSE_WARNING
    }
}

// to open modal
export function openProviderModal(provider) {
  return {
    type: actionTypes.OPEN_PROVIDER_MODAL,
    provider
  }
}

// to open archive modal
export function openArchiveModal(provider) {
  return {
    type: actionTypes.OPEN_ARCHIVE_PROVIDER_MODAL,
    provider
  }
}

export function openLmsProviderModal(provider, action) {
   return {
    type: actionTypes.OPEN_LMS_PROVIDER_MODAL,
    provider,
    action
  }
}

// to open source modal
export function openSourceModal(source) {
  return {
    type: actionTypes.OPEN_SOURCE_MODAL,
    source
  }
}

export function openSourceReportModal(source) {
  return {
    type: actionTypes.OPEN_SOURCE_REPORT_MODAL,
    source
  }
}

// to open content count modal
export function openContentCountModal(source) {
  return {
    type: actionTypes.OPEN_CONTENT_COUNT_MODAL,
    source
  }
}

// to open delete source modal
export function openDeleteSourceModal(source) {
  return {
    type: actionTypes.OPEN_DELETE_SOURCE_MODAL,
    source
  }
}

// to open delete source modal
export function openDeleteLmsSourceModal(source) {
  return {
    type: actionTypes.OPEN_DELETE_LMS_SOURCE_MODAL,
    source
  }
}

export function openAddCollabConfirmModal(data) {
  return {
    type: actionTypes.OPEN_ADD_COLLAB_CONFIRM_MODAL,
    data
  }
}

export function openBoxProviderModal(provider) {
  return {
    type: actionTypes.OPEN_BOX_PROVIDER_MODAL,
    provider
  }
}

export function openSharepointTokenBasedProviderModal(provider) {
  return {
    type: actionTypes.OPEN_SHAREPOINT_TOKEN_BASED_PROVIDER_MODAL,
    provider
  }
}

export function openAddPartnerCenterModal() {
  return {
    type: actionTypes.OPEN_ADD_PARTNER_CENTER_MODAL,
  }
}

export function openEditPartnerCenter(partner_center) {
  return {
    type: actionTypes.OPEN_EDIT_PARTNER_CENTER_MODAL,
    partner_center
  }
}

export function openUserImportStatusModal(data) {
  return {
    type: actionTypes.OPEN_USER_IMPORT_STATUS_MODAL,
    data
  }
}

export function openWorkflowStatusModal(data) {
  return {
    type: actionTypes.OPEN_WORKFLOW_STATUS_MODAL,
    data
  }
}

export function openHrmsJobUsersModal(data, jobId) {
  return {
    type: actionTypes.OPEN_HRMS_JOB_USERS_MODAL,
    data,
    jobId
  }
}

export function openOauth2Modal(data) {
  return {
    type: actionTypes.OPEN_OAUTH2_MODAL,
    data
  }
}

export function closeOauth2Modal() {
    return {
        type: actionTypes.CLOSE_MODAL
    }
}

// to open LMS archive modal
export function openLmsArchiveModal(provider) {
  return {
    type: actionTypes.OPEN_LMS_ARCHIVE_MODAL,
    provider
  }
}

export function openClcEditModal(data) {
  return {
    type: actionTypes.OPEN_CLC_EDIT_MODAL,
    data
  }
}

export function closeClcEditModal() {
  return {
    type: actionTypes.CLOSE_CLC_EDIT_MODAL
  }
}

export function updateClc(data) {
  return {
    type: actionTypes.UPDATE_CLC,
    data
  }
}

export function deleteClc(data) {
  return {
    type: actionTypes.DELETE_CLC,
    data
  }
}

export function deleteClcModal(data) {
  return {
    type: actionTypes.DELETE_CLC_MODAL,
    data
  }
}

export function closeDeleteClcModal() {
  return {
    type: actionTypes.CLOSE_DELETE_CLC_MODAL
  }
}

export function activeInactiveClcModal(data) {
  return {
    type: actionTypes.ACTIVE_INACTIVE_CLC_MODAL,
    data
  }
}

export function closeActiveInactiveClcModal() {
  return {
    type: actionTypes.CLOSE_ACTIVE_INACTIVE_CLC_MODAL
  }
}

export function openSourceExceptionLogsModal(provider) {
  return {
    type: actionTypes.OPEN_SOURCE_EXCEPTION_LOGS_MODAL,
    provider
  }
}

export function closeDynamicGroupDeleteModal() {
  return {
    type: actionTypes.CLOSE_DYNAMIC_GROUP_DELETE_MODAL
  }
}

export function openDynamicGroupDeleteModal(data) {
  return {
    type: actionTypes.OPEN_DYNAMIC_GROUP_DELETE_MODAL,
    data
  }
}

export function deletedWorkflow(data) {
  return {
    type: actionTypes.DELETED_DYNAMIC_WORKFLOW,
    data
  }
}

export function openDynamicGroupViewModal(data) {
  return {
    type: actionTypes.OPEN_DYNAMIC_GROUP_VIEW_MODAL,
    data
  }
}

export function closeDynamicGroupViewModal() {
  return {
    type: actionTypes.CLOSE_DYNAMIC_GROUP_VIEW_MODAL
  }
}

export function closeDynamicPreviewGroupName() {
  return {
    type: actionTypes.CLOSE_DYNAMIC_PREVIEW_GROUP_NAME
  }
}
export function openLmsConfigModal(data) {
  return {
    type: actionTypes.OPEN_LMS_CONFIG_MODAL,
    data
  }
}

export function createWso2CredentialModal() {
  return {
    type: actionTypes.CREATE_WSO2_CREDENTIAL_MODAL
  }
}

export function editWso2CredentialModal(application) {
  return {
    type: actionTypes.EDIT_WSO2_CREDENTIAL_MODAL,
    application
  }
}

export function closeWso2CredentialModal() {
    return {
        type: actionTypes.CLOSE_MODAL
    }
}

export function openContentImportStatusModal(data) {
  return {
    type: actionTypes.OPEN_CONTENT_IMPORT_STATUS_MODAL,
    data
  }
}

export function createKongCredentialModal() {
  return {
    type: actionTypes.SHOW_MODAL,
    modalType: modalTypes.KONG_CREDENTIAL_CREATE
  };
}

export function editKongCredentialModal(application) {
  if (!application) {
    console.error("No application data provided to editKongCredentialModal");
    return {
      type: actionTypes.SHOW_ALERT,
      message: "Error loading credential details",
      page: "kongCredentials",
      alertType: "error"
    };
  }
  
  return {
    type: actionTypes.SHOW_MODAL,
    modalType: 'KONG_CREDENTIAL_EDIT',
    modalProps: application
  };
}

export function closeKongCredentialModal() {
  return {
    type: actionTypes.CLOSE_MODAL
  };
}
