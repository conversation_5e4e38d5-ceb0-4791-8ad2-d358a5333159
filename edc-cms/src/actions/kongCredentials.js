import * as actionTypes from '../constants/actionTypes';
import { 
  createCredentials as kongCreateCredentials, 
  getKeys as kongGetKeys, 
  deleteCredentials as kongDeleteCredentials, 
  applicationDetails as kongApplicationDetails,
  updateApplication as kongUpdateApplication
} from 'edc-web-sdk/requests/kongCredentials';

export function createCredentials(payload) {
  return dispatch => {
    dispatch({ type: actionTypes.SHOW_ALERT, message: "Creating credentials...", page: "kongCredentials" });
    
    const kongPayload = {
      kong: payload
    };
    
    return kongCreateCredentials(kongPayload)
      .then(data => {
        dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        dispatch({ type: actionTypes.SHOW_ALERT, message: "Credentials created successfully", page: "kongCredentials" });
        dispatch(getKeys());
        setTimeout(() => {
          dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        }, 1000);
      })
      .catch(err => {
        dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        dispatch({ 
          type: actionTypes.SHOW_ALERT, 
          message: err.message || "Failed to create credentials", 
          page: "kongCredentials",
          type: "error" 
        });
        setTimeout(() => {
          dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        }, 3000);
      });
  };
}

export function getKeys() {
  return dispatch => {
    dispatch({ type: actionTypes.KONG_CREDENTIALS_LOADING });
    
    return kongGetKeys()
      .then(data => {
        dispatch({
          type: actionTypes.KONG_CREDENTIALS_LOADED,
          kongCredentials: data
        });
        return data;
      })
      .catch(err => {
        dispatch({
          type: actionTypes.KONG_CREDENTIALS_LOAD_ERROR,
          error: err
        });
      });
  };
}

export function deleteCredentials(id) {
  return dispatch => {
    dispatch({ type: actionTypes.SHOW_ALERT, message: "Deleting credentials...", page: "kongCredentials" });
    
    return kongDeleteCredentials(id)
      .then(() => {
        dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        dispatch({ type: actionTypes.SHOW_ALERT, message: "Credentials deleted successfully", page: "kongCredentials" });
        dispatch(getKeys());
        setTimeout(() => {
          dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        }, 1000);
      })
      .catch(err => {
        dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        dispatch({ 
          type: actionTypes.SHOW_ALERT, 
          message: err.message || "Failed to delete credentials", 
          page: "kongCredentials",
          alertType: "error" 
        });
        setTimeout(() => {
          dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        }, 3000);
      });
  };
}

export function applicationDetails(id) {
  return dispatch => {
    return new Promise((resolve, reject) => {
      kongApplicationDetails(id)
        .then(data => {
          resolve(data);
        })
        .catch(err => {
          reject(err);
        });
    });
  };
}

export function updateCredentials(payload) {
  return dispatch => {
    dispatch({ type: actionTypes.SHOW_ALERT, message: "Updating credentials...", page: "kongCredentials" });
    
    const kongPayload = { kong: payload };
    
    return kongUpdateApplication(payload.id, kongPayload)
      .then(data => {
        dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        dispatch({ type: actionTypes.SHOW_ALERT, message: "Credentials updated successfully", page: "kongCredentials" });
        dispatch(getKeys());
        setTimeout(() => {
          dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        }, 1000);
      })
      .catch(err => {
        dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        dispatch({ 
          type: actionTypes.SHOW_ALERT, 
          message: err.response?.body?.message || "Failed to update credentials", 
          page: "kongCredentials",
          alertType: "error" 
        });
        setTimeout(() => {
          dispatch({ type: actionTypes.CLOSE_ALERT, page: "kongCredentials" });
        }, 3000);
      });
  };
}