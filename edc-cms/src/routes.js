import { translatr } from '@translatr/utils';
import React from 'react';
import { IndexRoute, Route } from 'react-router';
import { SidebarNav, SidebarNavItem } from '@sketchpixy/rubix';
import cx from 'classnames';
import LD from './LDStore';
import { getConfigsByName } from 'edc-web-sdk/requests/orgSettings.v2';

/* Pages */
import AnalyticsContainer from './components/Analytics';
import ChannelsAnalyticsContainer from './components/Analytics/Channels';
import GroupsAnalyticsContainer from './components/Analytics/Groups';
import ContentAnalyticsContainer from './components/Analytics/Content';
import AssignmentAnalyticsContainer from './components/Analytics/Assignment';
import AssessmentAnalyticsContainer from './components/Analytics/Assessment';
import KnowledgeMapContainer from './components/Analytics/KnowledgeMap';
import KnowledgeMapNewContainer from './components/Analytics/KnowledgeMapNew';
import KnowledgeGraphContainer from './components/Analytics/KnowledgeGraph';
import SourcesContainer from './components/sources/SourcesContainer';
import Users from './components/Users/<USER>/userContainer.jsx';
import Taxonomy from './components/approvals/taxonomy';
import Role from './components/Users/<USER>/roleContainer.jsx';
import LiveStream from './components/liveStream/liveStreamContainer.jsx';
import ChannelContainer from './components/channels/ChannelBase';
import AddChannelContainer from './components/channels/AddChannelContainer';
import ContentsContainer from './components/contents/ContentsContainer';
import CoursesContainer from './components/courses/CoursesContainer';
import Settings from './components/settings/settings.jsx';
import ContinuousLearningCredits from './components/settings/ContinuousLearningCredits.jsx';
import Configuration from './components/settings/configuration.jsx';
import Navigation from './components/settings/navigation.jsx';
import GroupsManager from './components/GroupsManager/container.jsx';
import CircleManager from './components/CircleManager/Container.jsx';
import EclConfigContainer from './components/ecl/EclConfigContainer';
import featuredProviders from './components/Integrations/featuredProviders';
import Integrations from './components/Integrations/Integrations.jsx';
import Credentials from './components/settings/credentials.jsx';
import Notifications from './components/settings/notifications.jsx';
import CustomFields from './components/settings/customFields.jsx';
import SearchResults from './components/settings/searchResults.jsx';
import XAPIContainer from './components/Integrations/XAPIContainer';
import OAuth2Container from './components/Integrations/OAuth2Container';
import HRMSContainer from './components/Integrations/HRMSContainer.jsx';
import ImportPage from './components/Import/importPage';
import eclConfigurations from './components/Integrations/eclConfigurations';
import lmsConfigurations from './components/Integrations/lmsConfigurations';
import eclSuperAdminConfigurations from './components/Integrations/eclSuperAdminConfigurations';
import OrgNotifications from './components/triggers/OrgNotifications';
import CarouselContainer from './components/carousels/CarouselContainer';
import EditCarouselContainer from './components/carousels/EditCarouselContainer';
import RelevancyRating from './components/RelevancyRating/RelevancyRating';
import AnnouncementsContainer from './components/announcements/AnnouncementsContainer';
import PartnerCenterContainer from './components/Integrations/PartnerCenterContainer';
import CardsContainer from './components/cards/CardsContainer';
import CompletionsContainer from './components/completions/CompletionsContainer';
import ReportedContainer from './components/reported/ReportedContainer';
import ReportedCommentContainer from './components/reported/ReportedCommentContainer';
import EmbargoContainer from './components/embargo/EmbargoContainer';
import CustomizationContainer from './components/emailCustomization/CustomizationContainer.jsx';
import WorkflowContainer from './components/workflow/WorkflowContainer.jsx';
import WorkflowContainerV2 from './components/workflow/WorkflowContainerV2.jsx';
import WorkflowEditor from './components/workflow/WorkflowEditor.jsx';
import ManageEmailTemplates from './components/manageEmailTemplates/ManageEmailTemplatesContainer';
import ManageSenders from './components/manageEmailTemplates/ManageSenders';
import UserImportStatus from './components/UserImportStatus/ImportStatusContainer.jsx';
import ManageContentType from './components/settings/ManageContentType.jsx';
import SkillsFramework from './components/settings/skillsFramework.jsx';
import TalentFramework from './components/settings/talentFramework.jsx';
import TalentDevelopment from './components/settings/telentDevelopment.jsx';
import SkillsStudio from './components/settings/skillsStudio.jsx';
import DynamicWorkflowListContainer from './components/workflow/dynamic/DynamicWorkflowListContainer.jsx';
import DynamicWorkflowCreateContainer from './components/workflow/dynamic/DynamicWorkflowCreateContainer.jsx';
import MkpAdminContainer from './components/Users/<USER>/MkpAdminContainer.jsx';
import DataAnalytics from './components/DataAnalytics/index.jsx';
import ThemeBrandingSetting from './components/branding/themeBrandingSetting/ThemeBrandingSetting';
import LoginSetting from './components/branding/loginSetting/LoginSetting';
import ControlledTalentMarketplaceRoute from './components/TalentMarketplace/shared/ControlledRoute';
import ControlledHRDataRoute from './components/HRData/shared/ControlledRoute';
import LMSConfig from './components/Integrations/LMSConfigPage';
import ContentStudioConfig from './components/Integrations/ContentStudioConfigPage';
import Languages from './components/settings/languages';
import EdCampaign from './components/settings/EdCampaign';
import SsoSetting from './components/branding/ssoSetting/SsoSetting';
import SkillsAssessment from './components/settings/SkillsAssessment/SkillsAssessment.jsx';
import DynamicPolicyEngineCreate from './components/PolicyEngine/DynamicPolicyEngineCreate.jsx';
import PolicyEngine from './components/PolicyEngine/PolicyEngine.jsx';
import ConfigService from './components/settings/ConfigService/index.jsx';
import SearchSettings from './components/SearchSettings';
import ContentImportStatusContainer from './components/ContentImportStatus/ContentImportStatusContainer';
import HomePage from './components/settings/HomePage/index.jsx';
import IpConfiguration from "./components/settings/IpConfiguration/index.jsx"

import App from './App';

import { Permissions } from '../common/utils/checkPermissions';
import Go1Integration from './components/Integrations/Go1Integration';
import WorkatoLanding from './components/workato/index.jsx';
import Wso2CredentialContainer from './components/wso2/Wso2CredentialContainer';
import { JWT } from 'edc-web-sdk/requests/csrfToken';
import { ENABLEV2V5 } from './actions/enablev2v5';
import KongCredentialContainer from './components/kong/KongCredentialContainer';
/* Pages for sub-admins */
const canManageContent = ['Reported Comment', 'Reported Content', 'Embargo Sites'];
const canManageLimitedContent = ['SmartCards', 'Pathways', 'Journeys', 'Live Stream', 'Courses'];
const canManageChannels = ['Channels'];
const canManageAnalytics = [
  'Dashboard',
  'Group Performance',
  'User Performance',
  'Content Performance',
  'Assignment Performance',
  'Assessment Performance'
];
const canManageAnalyticsLimited = [
  'Dashboard',
  'Group Performance',
  'User Performance',
  'Content Performance',
  'Assignment Performance'
];
const canManageIntegrations = [
  'Extensions',
  'xAPI',
  'Partner Centers',
  'HRMS',
  'Go1 Integration',
  'Integrations',
  'LMS Integrations'
];
const canManageNotifications = [
  'Organization Level',
  'Email Customization',
  'Manage Email Templates',
  'Manage Senders',
  'Notifications'
];
const canManageSite = [
  'Team',
  'Configuration',
  'Navigation',
  'Continuous Learning',
  'Announcements',
  'Login page settings',
  'Notifications',
  'Manage Content Type',
  'Custom Carousels',
  'General',
  'Login',
  'Header'
];
const canManageSso = ['SSO'];
// Menuoptions for View Roles permission
const canViewUsersAndRoles = ['Users', 'Roles'];
const canManageChannelAnalytics = ['Channel Performance'];
const canManageAccounts = ['Users', 'Custom Fields', 'User Import Status'];
const canManageGroups = ['Groups', 'Multi Org', 'Dynamic Group Management'];
const canManageRoles = ['Roles'];
const canManageAccountsLimited = ['Users'];
const canManageDgm = ['Dynamic Group Management'];
const canManageTalentMarketplace = [
  'General',
  'Job Role',
  'Job Vacancy',
  'Project',
  'Mentorship',
  'Sourcing'
];
const canManageEdConnect = ['API Keys', 'Analytics'];

const ManageMultiOrgMainTabsToShow = [
  'Content',
  'Accounts',
  'Branding',
  'Settings',
  'Notifications'
];
const ManageMultiOrgSubTabs = [
  'SmartCards',
  'Pathways',
  'Journeys',
  'Reported Content',
  'Reported Comment',
  'Channels',
  'Custom Carousels',
  'Users',
  'Groups',
  'User Import Status',
  'Configuration',
  'Custom Fields',
  'General',
  'Header',
  'Organization Level',
  'Email Customization',
  'Manage Email Templates',
  'Manage Senders',
  'Team'
];

const MultiOrgMainTabsNotToShowForParentOrg = ['Notifications'];
const MultiOrgSubTabsNotToShowForParentOrg = [
  'Notifications',
  'Organization Level',
  'Email Customization',
  'Manage Email Templates',
  'Manage Senders'
];

const ManageHrDataMenu = [
  'Job Functions',
  'Job Families',
  'Job Roles',
  'Organizational Units',
  'Location',
  'Configuration'
];

const canManageDiscover = ['discover', 'Custom Carousels'];
const canManageAnnouncements = ['Announcements'];

const canManageGeneralBranding = ['Theme & Branding'];
const canManageHeaderBranding = ['Header'];
const canManageLoginBranding = ['Login'];

/* Global routes configuration object */
const routes = {
  IndexRoute: { component: AnalyticsContainer },
  Analytics: [
    { path: 'admin', component: AnalyticsContainer },
    {
      path: 'admin/analytics/dashboard',
      component: AnalyticsContainer,
      i18nKey: 'dashboard',
      name: 'Dashboard'
    },
    {
      path: 'admin/analytics/groups',
      component: GroupsAnalyticsContainer,
      i18nKey: 'groupPerformance',
      name: 'Group Performance'
    },
    {
      path: 'admin/analytics/content',
      component: ContentAnalyticsContainer,
      i18nKey: 'contentPerformance',
      name: 'Content Performance'
    },
    { path: 'admin/analytics/knowledge-map', component: KnowledgeMapContainer },
    { path: 'admin/analytics/knowledge-map-new', component: KnowledgeMapNewContainer },
    {
      path: 'admin/analytics/knowledge-graph',
      component: KnowledgeGraphContainer,
      i18nKey: 'knowledgeGraph',
      name: 'Knowledge Graph'
    },
    {
      path: 'admin/analytics/assignment',
      component: AssignmentAnalyticsContainer,
      i18nKey: 'assignmentPerformance',
      name: 'Assignment Performance'
    },
    {
      path: 'admin/analytics/assessments',
      component: AssessmentAnalyticsContainer,
      i18nKey: 'assessmentPerformance',
      name: 'Assessment Performance'
    },
    {
      path: 'admin/analytics/channels',
      component: ChannelsAnalyticsContainer,
      i18nKey: 'channelPerformance',
      name: 'Channel Performance'
    }
  ],
  Content: [
    { path: 'admin/content/sources', component: SourcesContainer },
    // @FYI https://jira.edcastcloud.com/browse/EP-32976
    // this route is default route if LD flag performance-analytics is TRUE
    {
      path: 'admin/content/items',
      component: ContentsContainer,
      i18nKey: 'smartCards',
      name: 'SmartCards'
    },
    {
      path: 'admin/content/stream',
      component: LiveStream,
      i18nKey: 'liveStream',
      name: 'Live Stream'
    },
    {
      path: 'admin/content/pathways',
      component: CardsContainer,
      params: 'pathway',
      i18nKey: 'pathways',
      name: 'Pathways'
    },
    {
      path: 'admin/content/journeys',
      component: CardsContainer,
      params: 'journey',
      i18nKey: 'journeys',
      name: 'Journeys'
    },
    {
      path: 'admin/content/completions',
      component: CompletionsContainer,
      i18nKey: 'completions',
      name: 'Completions'
    },
    {
      path: 'admin/content/reported',
      component: ReportedContainer,
      i18nKey: 'reportedContent',
      name: 'Reported Content'
    },
    {
      path: 'admin/content/reported_comments',
      component: ReportedCommentContainer,
      i18nKey: 'reportedComment',
      name: 'Reported Comment'
    },
    {
      path: 'admin/content/embargo',
      component: EmbargoContainer,
      i18nKey: 'embargoSites',
      name: 'Embargo Sites'
    },
    {
      path: 'admin/course/courses',
      component: CoursesContainer,
      i18nKey: 'courses',
      name: 'Courses'
    },
    {
      path: 'admin/content/relevancyRating',
      component: RelevancyRating,
      i18nKey: 'relevancyRatings',
      name: 'Relevancy Ratings'
    },
    {
      path: 'admin/integrations/eclConfigurations',
      component: eclConfigurations,
      i18nKey: 'integrations',
      name: 'Integrations'
    },
    {
      path: 'admin/integrations/lmsConfigurations',
      component: lmsConfigurations,
      i18nKey: 'lmsIntegrations',
      name: 'LMS Integrations'
    },
    {
      path: 'admin/channel/channels',
      component: ChannelContainer,
      i18nKey: 'channels',
      name: 'Channels'
    },
    { path: 'admin/channel/addChannel', component: AddChannelContainer },
    { path: 'admin/channel/:id', component: AddChannelContainer },
    {
      path: 'admin/carousel/carousels',
      component: CarouselContainer,
      i18nKey: 'customCarousels',
      name: 'Custom Carousels'
    },
    { path: 'admin/carousel/:id', component: EditCarouselContainer },
    {
      path: 'admin/content/import_status',
      component: ContentImportStatusContainer,
      i18nKey: 'contentUpdateImportStatus',
      name: 'Content update import status'
    }
  ],
  Accounts: [
    { path: 'admin/user/users', component: Users, i18nKey: 'users', name: 'Users' },
    { path: 'admin/user/role', component: Role, i18nKey: 'roles', name: 'Roles' },
    { path: 'admin/groups', component: GroupsManager, i18nKey: 'groups', name: 'Groups' },
    { path: 'admin/multi-org', component: CircleManager, i18nKey: 'multiOrg', name: 'Multi Org' },
    { path: 'admin/import', component: ImportPage },
    {
      path: 'admin/user/import_status',
      component: UserImportStatus,
      i18nKey: 'userImportStatus',
      name: 'User Import Status'
    },
    {
      path: 'admin/workflow/dynamic',
      component: DynamicWorkflowListContainer,
      i18nKey: 'dynamicGroupManagement',
      name: 'Dynamic Group Management'
    },
    { path: 'admin/workflow/dynamic/create', component: DynamicWorkflowCreateContainer },
    { path: 'admin/workflow/dynamic/edit/:id', component: DynamicWorkflowCreateContainer },
    {
      path: 'admin/user/mkpAdmins',
      component: MkpAdminContainer,
      i18nKey: 'coursesAndEventsAdmins',
      name: 'Courses And Events Admins'
    },
    {
      path: 'admin/policy-engine',
      component: PolicyEngine,
      i18nKey: 'GroupBasedAccessControl',
      name: 'Group Based Access Control'
    },
    { path: 'admin/policy-engine/:id', component: DynamicPolicyEngineCreate },
    { path: 'admin/policy-engine/create', component: DynamicPolicyEngineCreate }
  ],
  Settings: [
    { path: 'admin/settings(/:settingId)', component: Settings, i18nKey: 'team2', name: 'Team' },
    {
      path: 'admin/data-analytics',
      component: DataAnalytics,
      i18nKey: 'dataAndAnalytics',
      name: 'Data and Analytics'
    },
    { path: 'admin/search-settings', component: SearchSettings, i18nKey: 'Search', name: 'Search' },
    {
      path: 'admin/configuration(/:tabId/:settingId)',
      component: Configuration,
      i18nKey: 'configuration',
      name: 'Configuration'
    },
    {
      path: 'admin/navigation',
      component: Navigation,
      i18nKey: 'Navigation',
      name: 'Navigation'
    },
    {
      path: 'admin/continuous-learning-credits',
      component: ContinuousLearningCredits,
      i18nKey: 'continuousLearning',
      name: 'Continuous Learning'
    },
    {
      path: 'admin/announcements',
      component: AnnouncementsContainer,
      i18nKey: 'announcements',
      name: 'Announcements'
    },
    {
      path: 'admin/credentials',
      component: Credentials,
      i18nKey: 'apiCredentials',
      name: 'API Credentials'
    },
    {
      path: 'admin/skills-framework',
      component: SkillsFramework,
      i18nKey: 'talentDevelopment2',
      name: 'Talent Development '
    },
    {
      path: 'admin/talent-framework',
      component: TalentFramework,
      i18nKey: 'ocgTalentDevelopment',
      name: 'OCG Talent Development '
    },
    { path: 'admin/ecl-config', component: EclConfigContainer },
    { path: 'admin/search/:term', component: SearchResults },
    {
      path: 'admin/manage-content-type',
      component: ManageContentType,
      i18nKey: 'manageContentType',
      name: 'Manage Content Type'
    },
    {
      path: 'admin/custom-fields',
      component: CustomFields,
      i18nKey: 'customFields',
      name: 'Custom Fields'
    },
    { path: 'admin/languages', component: Languages, i18nKey: 'languagesmenu', name: 'Languages' },
    { path: 'admin/edcampaign', component: EdCampaign, i18nKey: 'Campaigns', name: 'Campaigns' },
    {
      path: 'admin/skills-assessment',
      component: SkillsAssessment,
      i18nKey: 'SkillsAssessment',
      name: 'Skills Assessment'
    },
    {
      path: 'admin/config-service/',
      component: ConfigService,
      i18nKey: 'ConfigService',
      name: 'ConfigService'
    },
    { path: 'admin/homepage/', component: HomePage, i18nKey: 'Homepage', name: 'Home Page' },
    { path: 'admin/config-service/services/:productId', component: ConfigService },
    {
      path: 'admin/config-service/services/:productId/features/:serviceId',
      component: ConfigService
    },
    {
      path: 'admin/config-service/services/:productId/features/:serviceId/flags/:featureId',
      component: ConfigService
    },
    { path: 'admin/ip-configuration', component: IpConfiguration, i18nKey: 'ipConfiguration', name: 'IP Configuration' },
  ],
  'Skills Management': [
    {
      path: 'admin/skills-studio',
      component: SkillsStudio,
      i18nKey: 'skillsStudio',
      name: 'Skills Studio'
    },
    {
      path: 'admin/talent-development',
      component: TalentDevelopment,
      i18nKey: 'talentDevelopmentV2',
      name: 'Talent Development V2'
    }
  ],
  Branding: [
    {
      path: 'admin/themeAndBranding',
      component: ThemeBrandingSetting,
      i18nKey: 'themeAndBranding',
      name: 'Theme & Branding'
    },
    { path: 'admin/login(/:loginId)', component: LoginSetting, i18nKey: 'login', name: 'Login' },
    { path: 'admin/sso(/:ssoId)', component: SsoSetting, i18nKey: 'Sso', name: 'SSO' }
  ],
  Integrations: [
    {
      path: 'admin/integrations',
      component: Integrations,
      i18nKey: 'extensions',
      name: 'Extensions'
    },
    { path: 'admin/integrations/featuredProviders', component: featuredProviders },
    { path: 'admin/integrations/eclSettings', component: eclSuperAdminConfigurations },
    { path: 'admin/integrations/xapi', component: XAPIContainer, i18nKey: 'xapi', name: 'xAPI' },
    {
      path: 'admin/integrations/partnerCenters',
      component: PartnerCenterContainer,
      i18nKey: 'partnerCenters',
      name: 'Partner Centers'
    },
    {
      path: 'admin/integrations/oauth2',
      component: OAuth2Container,
      i18nKey: 'oAuth2Apps',
      name: 'OAuth2 Apps'
    },
    { path: 'admin/integrations/HRMS', component: HRMSContainer, i18nKey: 'hrms', name: 'HRMS' },
    {
      path: 'admin/integrations/go1-integration',
      component: Go1Integration,
      i18nKey: 'go1Integration',
      name: 'Go1 Integration'
    },
    {
      path: 'admin/integrations/lmsConfig',
      component: LMSConfig,
      i18nKey: 'lmsConfiguration',
      name: 'LMS Configuration'
    },
    {
      path: 'admin/integrations/contentStudioConfig',
      component: ContentStudioConfig,
      i18nKey: 'ContentStudioConfig',
      name: 'Content Studio'
    }
  ],
  Workflows: [
    {
      path: 'admin/workflow/create',
      component: WorkflowContainerV2,
      i18nKey: 'createWorkflow',
      name: 'Create Workflow'
    },
    { path: 'admin/workflow/new', component: WorkflowEditor }
  ],
  Notifications: [
    {
      path: 'admin/notifications',
      component: Notifications,
      i18nKey: 'notifications',
      name: 'Notifications'
    },
    {
      path: 'admin/org-notifications',
      component: OrgNotifications,
      i18nKey: 'organizationLevel',
      name: 'Organization Level'
    },
    {
      path: 'admin/email-customization(/:id)',
      component: CustomizationContainer,
      i18nKey: 'emailCustomization',
      name: 'Email Customization'
    },
    {
      path: 'admin/manage-email-templates',
      component: ManageEmailTemplates,
      i18nKey: 'manageEmailTemplates',
      name: 'Manage Email Templates'
    },
    {
      path: 'admin/manage-senders',
      component: ManageSenders,
      i18nKey: 'manageSenders',
      name: 'Manage Senders'
    }
  ],
  Approvals: [
    {
      path: 'admin/taxonomy',
      component: Taxonomy,
      i18nKey: 'taxonomyApprovals',
      name: 'Taxonomy Approvals'
    }
  ],
  'Integration Hub': [
    { path: 'admin/workato/', component: WorkatoLanding, i18nKey: 'project', name: 'Project' },
    {
      path: 'admin/workato/custom_adapters',
      i18nKey: 'connector',
      name: 'Connector',
      component: WorkatoLanding
    },
    {
      path: 'admin/workato/dashboard/main',
      i18nKey: 'dashboard',
      name: 'Dashboard',
      component: WorkatoLanding
    },
    {
      path: 'admin/workato/lookup_tables',
      i18nKey: 'lookupTable',
      name: 'Lookup Table',
      component: WorkatoLanding
    },
    {
      path: 'admin/workato/account_properties',
      i18nKey: 'properties',
      name: 'Properties',
      component: WorkatoLanding
    }
  ],
  'Opportunity Marketplace': [
    {
      path: 'admin/talentmarketplace/general',
      component: ControlledTalentMarketplaceRoute,
      i18nKey: 'general',
      name: 'General'
    },
    {
      path: 'admin/talentmarketplace/general/recommendations-config/match-level/translation/:id',
      component: ControlledTalentMarketplaceRoute
    },
    {
      path: 'admin/talentmarketplace/job-role',
      component: ControlledTalentMarketplaceRoute,
      i18nKey: 'jobRole',
      name: 'Job Role'
    },
    {
      path: 'admin/talentmarketplace/job-vacancy',
      component: ControlledTalentMarketplaceRoute,
      i18nKey: 'jobVacancy',
      name: 'Job Vacancy'
    },
    {
      path: 'admin/talentmarketplace/project',
      component: ControlledTalentMarketplaceRoute,
      i18nKey: 'project',
      name: 'Project'
    },
    {
      path: 'admin/talentmarketplace/mentorship',
      component: ControlledTalentMarketplaceRoute,
      i18nKey: 'mentorship',
      name: 'Mentorship'
    },
    {
      path: 'admin/talentmarketplace/sourcing',
      component: ControlledTalentMarketplaceRoute,
      i18nKey: 'sourcing',
      name: 'Sourcing'
    }
  ],
  'HR Data': [
    {
      path: 'admin/hrdata/job-functions',
      component: ControlledHRDataRoute,
      i18nKey: 'jobFunctions',
      name: 'Job Functions'
    },
    { path: 'admin/hrdata/job-functions/create', component: ControlledHRDataRoute },
    { path: 'admin/hrdata/job-functions/edit/:id', component: ControlledHRDataRoute },
    {
      path: 'admin/hrdata/job-families',
      component: ControlledHRDataRoute,
      i18nKey: 'jobFamilies',
      name: 'Job Families'
    },
    { path: 'admin/hrdata/job-families/create', component: ControlledHRDataRoute },
    { path: 'admin/hrdata/job-families/edit/:id', component: ControlledHRDataRoute },
    {
      path: 'admin/hrdata/job-roles',
      component: ControlledHRDataRoute,
      i18nKey: 'jobRoles',
      name: 'Job Roles'
    },
    { path: 'admin/hrdata/job-roles/create', component: ControlledHRDataRoute },
    { path: 'admin/hrdata/job-roles/edit/:id', component: ControlledHRDataRoute },
    {
      path: 'admin/hrdata/configuration',
      component: ControlledHRDataRoute,
      i18nKey: 'configuration',
      name: 'Configuration'
    },
    {
      path: 'admin/hrdata/organization',
      component: ControlledHRDataRoute,
      i18nKey: 'OrganizationalUnits',
      name: 'Organizational Units'
    },
    { path: 'admin/hrdata/organization/create', component: ControlledHRDataRoute },
    { path: 'admin/hrdata/organization/edit/:id', component: ControlledHRDataRoute },
    {
      path: 'admin/hrdata/job-functions/translation/:id',
      component: ControlledHRDataRoute
    },
    {
      path: 'admin/hrdata/job-families/translation/:id',
      component: ControlledHRDataRoute
    },
    {
      path: 'admin/hrdata/job-roles/translation/:id',
      component: ControlledHRDataRoute
    },
    {
      path: 'admin/hrdata/organization/translation/:id',
      component: ControlledHRDataRoute
    },
    {
      path: 'admin/hrdata/location',
      component: ControlledHRDataRoute,
      i18nKey: 'location',
      name: 'Location'
    },
    { path: 'admin/hrdata/location/create', component: ControlledHRDataRoute },
    { path: 'admin/hrdata/location/edit/:id', component: ControlledHRDataRoute },
    {
      path: 'admin/hrdata/location/translation/:id',
      component: ControlledHRDataRoute
    }
  ],
  // This is done intetionally to hide name but to keep route as it is
  EdConnect: [
    {
      path: 'admin/wso2/api',
      component: Wso2CredentialContainer,
      name: 'API Keys',
      i18nKey: 'EdConnectAPIKey'
    },
    {
      path: 'admin/kong/api',
      component: KongCredentialContainer,
      name: 'API Keys - V2',
      i18nKey: 'KongAPIKey'
    }
  ]
};

//Render routes for Server Side
export const serverRender = {
  get routes() {
    return (
      <Route path="/" component={App}>
        {this.sectionsFilter().map(section => {
          return this.RouteItem(routes[section]);
        })}
      </Route>
    );
  },
  sectionsFilter() {
    return Object.keys(routes).filter(section => {
      return section !== 'IndexRoute';
    });
  },
  RouteItem(routes) {
    return routes.map(route => {
      return <Route key={route.path} path={route.path} component={route.component} />;
    });
  }
};

//Render routes for Browser Side
export const browserRender = {
  get routes() {
    const isDynamicEnabled = Permissions.has('MANAGE_DGM') || Permissions.has('ADMIN_ONLY');
    if (
      this.alreadyRenderRoutes &&
      !browserRender.notificationsTriggersEnabled &&
      !browserRender.leaderboardEnabled
    ) {
      return this.alreadyRenderRoutes;
    } else {
      return (this.alreadyRenderRoutes = (
        <SidebarNav>
          {this.sectionsFilter().map(section => {
            const istwoLines = section === 'Workflows' && isDynamicEnabled;
            return (
              <SidebarNavItem
                key={section}
                glyph={this.getSectionIcon(section)}
                name={this.getSectionNameTranslation(section)}
              >
                <SidebarNav className={`${cx('second-menu', { 'two-lines': istwoLines })}`}>
                  {this.SidebarNavItem(this.routesFilter(routes[section]))}
                </SidebarNav>
              </SidebarNavItem>
            );
          })}
        </SidebarNav>
      ));
    }
  },
  getSectionNameTranslation(section) {
    const i18nKeys = {
      Analytics: 'analytics',
      Content: 'content',
      Accounts: 'accounts',
      Settings: 'settings',
      'Skills Management': 'skillsManagement',
      Branding: 'branding',
      Integrations: 'integrations',
      Workflows: 'workflows',
      Notifications: 'notifications',
      Approvals: 'approvals',
      'Integration Hub': 'integrationHub',
      'Opportunity Marketplace': 'talentMarketplace',
      'HR Data': 'hrData',
      EdConnect: 'LearningExperienceApis'
    };
    return translatr('common.common', i18nKeys[section], {}, section);
  },
  getSectionIcon(section) {
    const icons = {
      Analytics: 'icon-feather-bar-graph-2',
      Content: 'icon-feather-paper',
      Accounts: 'icon-fontello-users',
      Settings: 'icon-fontello-cog-alt',
      Integrations: 'icon-fontello-briefcase-1',
      Workflows: 'icon-fontello-split',
      Notifications: 'icon-fontello-email',
      Approvals: 'icon-fontello-check',
      Branding: 'fas fa-pencil-ruler',
      'Integration Hub': 'icon-feather-paper',
      'Skills Management': 'icon-fontello-tools',
      'Opportunity Marketplace': 'icon-fontello-hammer',
      'HR Data': 'icon-fontello-users-1',
      EdConnect: 'icon-fontello-sitemap'
    };
    return section !== 'Notifications' &&
      section !== 'Workflows' &&
      section !== 'Skills Management' &&
      section !== 'Opportunity Marketplace' &&
      section !== 'HR Data' &&
      section !== 'EdConnect'
      ? `icomoon-${section}`
      : icons[section];
  },
  /* Getting and Setting LD Flags status. To set a new flag, you must pass 2 and 3 argument*/
  flags(flagName, setFlag, status) {
    let hrEnableFlag = this.HrEnablementConfig();
    let flags = {
      clc: window.ldclient.variation('clc', false),
      relevancyRatings: window.ldclient.variation('relevancy-rating', false),
      lxpOAuth: window.ldclient.variation('enable-lxp-oauth-okta', false),
      customEmailTemplates: window.ldclient.variation('custom-emails', false),
      showNewLoginPage: window.ldclient.variation('new-login-page-settings', false),
      userNetwork: window.ldclient.variation('user-network', false),
      ocg:
        !hrEnableFlag &&
        Permissions.has('MANAGE_TALENT_DEVELOPMENT') &&
        window.ldclient.variation('ocg', false),
      ocg_test:
        !hrEnableFlag &&
        Permissions.has('MANAGE_TALENT_DEVELOPMENT') &&
        window.ldclient.variation('ocg_test', false),
      tfContentMapping:
        !hrEnableFlag &&
        Permissions.has('MANAGE_TALENT_DEVELOPMENT') &&
        window.ldclient.variation('tf-content-mapping', false),
      enableGo1Integration: window.ldclient.variation('enable-go1-integration', false),
      aws_eddata: window.ldclient.variation('aws-eddata', false)
    };
    if (flagName) {
      return flags[flagName];
    } else if (setFlag && status) {
      browserRender[setFlag] = status;
    }
  },
  sectionsFilter() {
    return Object.keys(routes).filter(section => {
      return (
        (section === 'Skills Management'
          ? this.flags('tfContentMapping')
          : section !== 'IndexRoute') &&
        this.checkPermission(null, section) &&
        this.checkOnFlag(null, section) &&
        this.multiOrgFilters(null, section)
      );
    });
  },
  routesFilter(section) {
    return section.filter(router => {
      return (
        router.hasOwnProperty('name') &&
        this.checkOnFlag(router) &&
        this.checkPermission(router) &&
        this.multiOrgFilters(router)
      );
    });
  },
  SidebarNavItem(routes) {
    return routes.map(route => {
      let path;

      if (~route.path.indexOf('(/:tabId/:settingId)')) {
        path = route.path.slice(0, -20);
      } else if (~route.path.indexOf('(/:settingId)')) {
        path = route.path.slice(0, -13);
      } else if (~route.path.indexOf('(/:id)')) {
        path = route.path.slice(0, -6);
      } else if (~route.path.indexOf('(/:brandingId)')) {
        path = route.path.slice(0, 13);
      } else if (~route.path.indexOf('(/:headerId)')) {
        path = route.path.slice(0, 12);
      } else if (~route.path.indexOf('(/:loginId)')) {
        path = route.path.slice(0, 11);
      } else if (~route.path.indexOf('(/:ssoId)')) {
        path = route.path.slice(0, 9);
      } else {
        path = route.path;
      }
      return (
        <SidebarNavItem
          key={path}
          href={`/${path}`}
          name={
            route.i18nKey === 'multiOrg'
              ? translatr('multiOrg.main', route.i18nKey, {}, route.name)
              : translatr('common.common', route.i18nKey, {}, route.name)
          }
        />
      );
    });
  },
  HrEnablementConfig() {
    const hrDataServiceConfig = window.__edOrgData.configs.find(
      config => config.name === 'hr_data_service_enablement'
    );

    return hrDataServiceConfig ? hrDataServiceConfig.value : false;
  },
  IsLaUserEnable() {
    const token = JWT.token;
    const payloadBase64 = token?.split('.')?.[1] || '';
    const decodedPayload = payloadBase64 && JSON.parse(atob(payloadBase64));
    return decodedPayload?.la_user;
  },
  /* Checking on Permissions */
  checkPermission(router, section) {
    let permissions = Permissions.isSubAdmin();
    const showHrData = this.HrEnablementConfig() && permissions.hrDataManager;
    const showTalentMarketplace = LD.careerGrowth() && permissions.talentMarketplaceManager;
    const isFlag = ENABLEV2V5.flag;
    let isLaUser = this.IsLaUserEnable();

    if (router) {
      let compArr = [];
      if (permissions.isAdminOnly) {
        if (router.name == 'SSO') {
          return permissions.ssoManager;
        }
        if (router.name == 'API Credentials') {
          return isLaUser || isFlag;
        }
        if (router.name == 'OAuth2 Apps') {
          return isLaUser || isFlag;
        }
        return true;
      } else {
        if (permissions.contentManager) {
          compArr = compArr.concat(canManageContent);
          compArr = compArr.concat(canManageLimitedContent);
        }
        if (permissions.limitedContent) {
          compArr = compArr.concat(canManageLimitedContent);
        }
        if (permissions.channelsManager) {
          compArr = compArr.concat(canManageChannels);
        }
        if (permissions.accountsManager) {
          compArr = compArr.concat(canManageAccounts);
        }
        if (permissions.groupManager) {
          compArr = compArr.concat(canManageGroups);
        }
        if (permissions.limitedAccounts) {
          compArr = compArr.concat(canManageAccountsLimited);
        }
        if (permissions.analyticsManager) {
          compArr = compArr.concat(canManageAnalytics);
        }
        if (permissions.limitedAnalytics) {
          compArr = compArr.concat(canManageAnalyticsLimited);
        }
        if (permissions.integrationManager) {
          compArr = compArr.concat(canManageIntegrations);
        }
        if (permissions.notificationManager) {
          compArr = compArr.concat(canManageNotifications);
        }
        if (permissions.siteManager) {
          compArr = compArr.concat(canManageSite);
        }
        if (permissions.ssoManager && permissions.isAdminOnly) {
          compArr = compArr.concat(canManageSso);
        }
        if (permissions.analyticsManager || permissions.limitedChannelsAnalytics) {
          compArr = compArr.concat(canManageChannelAnalytics);
        }
        if (permissions.roleManager) {
          compArr = compArr.concat(canManageRoles);
        }
        if (permissions.manageDgm) {
          compArr = compArr.concat(canManageDgm);
        }
        if (showTalentMarketplace) {
          compArr = compArr.concat(canManageTalentMarketplace);
        }
        if (showHrData) {
          compArr = compArr.concat(ManageHrDataMenu);
        }
        if (permissions.feedManager || permissions.discoverManager) {
          compArr = compArr.concat('Configuration');
        }
        if (permissions.discoverManager) {
          compArr = compArr.concat(canManageDiscover);
        }
        if (permissions.announcementsManager) {
          compArr = compArr.concat(canManageAnnouncements);
        }
        if (permissions.manageGeneralBranding) {
          compArr = compArr.concat(canManageGeneralBranding);
        }
        if (permissions.manageHeaderBranding) {
          compArr = compArr.concat(canManageHeaderBranding);
        }
        if (permissions.manageLoginBranding) {
          compArr = compArr.concat(canManageLoginBranding);
        }
        //Add Users and Roles defined above
        if (permissions.roleViewer) {
          compArr = compArr.concat(canViewUsersAndRoles);
        }
        return !!~compArr.indexOf(router.name);
      }
    } else if (section) {
      let sectionArr = [];
      if (permissions.isAdminOnly) {
        return true;
      } else {
        if (
          permissions.contentManager ||
          permissions.limitedContent ||
          permissions.channelsManager ||
          permissions.integrationManager ||
          permissions.siteManager ||
          permissions.discoverManager
        ) {
          sectionArr.push('Content');
        }
        if (
          permissions.accountsManager ||
          permissions.limitedAccounts ||
          permissions.groupManager ||
          permissions.roleManager ||
          permissions.roleViewer ||
          permissions.manageDgm
        ) {
          sectionArr.push('Accounts');
        }
        if (
          permissions.analyticsManager ||
          permissions.limitedAnalytics ||
          permissions.limitedChannelsAnalytics
        ) {
          sectionArr.push('Analytics');
        }
        if (permissions.integrationManager) {
          sectionArr.push('Integrations');
        }
        if (permissions.notificationManager) {
          sectionArr.push('Notifications');
        }

        if (
          permissions.siteManager ||
          permissions.accountsManager ||
          permissions.feedManager ||
          permissions.discoverManager ||
          permissions.announcementsManager
        ) {
          sectionArr.push('Settings');
        }

        if (
          permissions.siteManager ||
          permissions.ssoManager ||
          permissions.manageGeneralBranding ||
          permissions.manageHeaderBranding ||
          permissions.manageLoginBranding
        ) {
          sectionArr.push('Branding');
        }

        if (showTalentMarketplace) {
          sectionArr.push('Opportunity Marketplace');
        }
        if (showHrData) {
          sectionArr.push('HR Data');
        }

        return !!~sectionArr.indexOf(section);
      }
    }
  },
  multiOrgFilters(router, section) {
    const multiOrgEnabled = window.__edOrgData.configs.find(
      config => config.name === 'multi-org'
    ) || { value: false };
    const multiOrgCapability = multiOrgEnabled.value && Permissions.has('SUB_ORG_ADMIN_ONLY');
    if (multiOrgCapability) {
      return router
        ? ManageMultiOrgSubTabs.includes(router.name)
        : ManageMultiOrgMainTabsToShow.includes(section);
    }

    const multiOrgCapabilityParentOrg = multiOrgEnabled.value && Permissions.has('ADMIN_ONLY');
    if (multiOrgCapabilityParentOrg) {
      return router
        ? !MultiOrgSubTabsNotToShowForParentOrg.includes(router.name)
        : !MultiOrgMainTabsNotToShowForParentOrg.includes(section);
    }
    return router || section;
  },
  /* Checking on LD flags */
  checkOnFlag(router, section) {
    let permissions = Permissions.isSubAdmin();
    let showHrData = this.HrEnablementConfig() && permissions.hrDataManager;
    const showTalentMarketplace = LD.careerGrowth() && permissions.talentMarketplaceManager;
    const multiOrgEnabled = window.__edOrgData.configs.find(
      config => config.name === 'multi-org'
    ) || { value: false };
    if (router) {
      switch (router.name) {
        case 'Knowledge Graph' || 'Extensions':
          if (window._webConstants['SHOW_SIDEBAR']) return router;
          break;
        case 'Continuous Learning':
          if (this.flags('clc')) return router;
          break;
        case 'Relevancy Ratings':
          if (this.flags('relevancyRatings')) return router;
          break;
        case 'Reported Comment':
          return router;
        case 'Partner Centers':
          if (this.flags('lxpOAuth')) return router;
          break;
        case 'Go1 Integration':
          if (this.flags('enableGo1Integration')) return router;
          break;
        case 'Organization Level':
          if (browserRender.notificationsTriggersEnabled) return router;
          break;
        case 'User Performance':
          if (browserRender.leaderboardEnabled) return router;
          break;
        case 'Create Workflow':
          if (browserRender.simplified_workflow) return router;
          break;
        case 'Dynamic Group Management':
          if (Permissions.has('MANAGE_DGM') || Permissions.has('ADMIN_ONLY')) return router;
          break;
        case 'Courses And Events Admins':
          if (
            browserRender.lms_config &&
            browserRender.lms_config.authentication_url &&
            browserRender.lms_config.mkp_domain
          )
            return router;
          break;
        case 'Multi Org':
          if (this.flags('userNetwork') || multiOrgEnabled.value) return router;
          break;
        case 'Courses':
          if (browserRender.is_savannah_enabled) return router;
          break;
        case 'Manage Content Type':
          if (browserRender.content_type_standardization) return router;
          break;
        case 'Talent Development ':
          if (this.flags('ocg')) return router;
          break;
        case 'OCG Talent Development ':
          if (this.flags('ocg_test')) return router;
          break;
        case 'Talent Development V2':
          if (this.flags('tfContentMapping')) return router;
          break;
        case 'Data and Analytics':
          if (this.flags('aws_eddata')) return router;
          break;
        case 'Email Customization':
          if (this.flags('customEmailTemplates')) return router;
          break;
        case 'Manage Email Templates':
          if (this.flags('customEmailTemplates')) return router;
          break;
        case 'Manage Senders':
          if (this.flags('customEmailTemplates')) return router;
          break;
        case 'Integration Hub':
          if (window.enable_workato == true) return router;
          break;
        case 'Project':
          // There are two routes named "Project"
          switch (router.path) {
            case 'admin/workato/':
              if (window.enable_workato == true) return router;
              break;
            case 'admin/talentmarketplace/project':
              return router;
            default:
              break;
          }
          break;
        case 'Mentorship':
          return router;
        case 'Languages':
          return router;
        case 'Campaigns':
          if (window.enable_edcampaign == true) return router;
          break;
        case 'Skills Assessment':
          if (window.skills_assessment_enablement == true) return router;
          break;
        case 'Navigation':
          if (window.navigation_service === true) return router;
          break;
        case 'ConfigService':
          if (window.enable_config_service === true) return router;
          break;
        case 'Content Studio':
          if (LD.contentStudio()) return router;
          break;
        case 'API Keys - V2':
          if (window.enable_api_gateway_credentials === true) return router;
          break;
        default:
          return router;
      }
    } else if (section) {
      switch (section) {
        case 'Analytics':
          if (
            LD.isAnalyticsEnabled() &&
            (permissions.isAdminOnly ||
              permissions.limitedAnalytics ||
              permissions.limitedChannelsAnalytics ||
              permissions.analyticsManager) &&
            false
          )
            return section;
          break;
        case 'Notifications':
          if (browserRender.notificationsTriggersEnabled) return section;
          break;
        case 'Workflows':
          if (browserRender.simplified_workflow) return section;
          break;
        case 'Branding':
          return permissions.isAdminOnly ||
            permissions.siteManager ||
            permissions.manageGeneralBranding ||
            permissions.manageHeaderBranding ||
            permissions.manageLoginBranding
            ? section
            : null;
        case 'Integration Hub':
          return window.enable_workato == true ? section : null;
        case 'Opportunity Marketplace':
          return showTalentMarketplace? section : null;
        case 'HR Data':
          return showHrData ? section : null;
        case 'EdConnect':
          return permissions.isEdConnectManager && window.enable_edconnect ? section : null;
        default:
          return section;
      }
    }
  }
};
