import path from 'path';
import express from 'express';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import bodyParser from 'body-parser';
import Config from 'edc-web-sdk/config/customConfig';
import proxy from 'express-http-proxy';
import ApiBox from 'edc-web-sdk/server/api/box';
import getStringifyData from 'edc-web-sdk/helpers/csp';
import getCspData from 'edc-web-sdk/requests/csp';
import { serverRender } from './src/routes';
import { setupReducers, renderHTMLString } from '@sketchpixy/rubix/lib/node/redux-router';
import RubixAssetMiddleware from '@sketchpixy/rubix/lib/node/RubixAssetMiddleware';
import dotenv from 'dotenv';
import reducers from './src/reducers';

// Load environment variables
dotenv.config();
setupReducers(reducers);

let cspPolicy = '';
const port = process.env.PORT || 8080;
const app = express();

const apm = require('elastic-apm-node').start({
  serviceName: process.env.ELASTIC_APM_SERVICE_NAME || 'edc-cms',
  secretToken: process.env.ELASTIC_APM_SECRET_TOKEN || '',
  apiKey: process.env.ELASTIC_APM_API_KEY || '',
  serverUrl: process.env.ELASTIC_APM_SERVER_URL || 'http://localhost:8200',
  active: process.env.NODE_ENV !== 'development',
  maxQueueSize: parseInt(process.env.ELASTIC_APM_MAX_QUEUE_SIZE, 10) || 1024
});

(async () => {
  try {
    app.use(bodyParser.json());

    if (process.env.NODE_ENV !== 'development') {
      app.use(
        bodyParser.json({
          type: ['application/json', 'application/csp-report', 'application/reports+json']
        })
      );
      // This function updates CSP
      const updateCspPolicy = async () => {
        const newCspStringValue = await getCspData(`${process.env.PLATFORM_HOST}`);
        // need to parse it in json
        const newJson = JSON.parse(newCspStringValue);
        if (newJson && typeof newJson === 'object') {
          cspPolicy = getStringifyData(newJson);
        }
      };

      // Get the data from API and update the CSP Policy (initial level)
      updateCspPolicy();

      // API will fetch new data after every 60 mins
      //and update the CSP without restart of the server or rotating pods
      setInterval(() => {
        updateCspPolicy();
      }, 3600000); // Update every 60 minutes

      app.use((req, res, next) => {
        res.setHeader('X-Content-Type-Options', 'nosniff');
        next();
      });

      app.use((req, res, next) => {
        res.setHeader('X-XSS-Protection', '1; mode=block');
        next();
      });
    }

    app.disable('x-powered-by');
    app.use(compression());
    app.use(bodyParser.urlencoded({ extended: false }));
    app.use(cookieParser());

    const configMgr = new Config(app);
    let _cookies = null;

    // If we need to define a custom host
    // @cli: `HOST=hostname npm run dev`
    const host = process.env.HOST || 'www';
    // Development only proxy router

    if (process.env.NODE_ENV === 'development' && !process.env.ENV_HOST) {
      const proxyUrl = `${host}.lvh.me:4000`;
      const routes = [
        '/cms',
        '/api',
        '/ext_service',
        '/invitations',
        '/assets',
        '/org_ssos.json',
        '/org_ssos'
      ];

      // Loop through each route and apply specific proxy options
      routes.forEach(route => {
        const proxyOptions = {
          decorateRequest: (proxyReq, originalReq) => {
            if (Object.keys(originalReq.cookies).length === 0) {
              proxyReq.headers['Cookie'] = `_edcast_session=${_cookies['_edcast_session']}`;
            }
            return proxyReq;
          },
          forwardPath: function(req, res) {
            return route + require('url').parse(req.url).path;
          }
        };

        // Apply the proxy for the current route
        app.use(route, proxy(proxyUrl, proxyOptions));
      });
    }

    /**
     * Set our static path to /admin
     * This allows us to server content from Cloudfront without conflicting with other applications
     * Disabled etag to fix security issue with Apache Server ETag Header Information Disclosure
     */
    app.use('/admin', express.static(path.join(process.cwd(), 'public'), { etag: false }));
    app.set('views', path.join(process.cwd(), 'views'));
    app.set('view engine', 'pug');

    // Load Box OAuth APIs
    new ApiBox(app);

    const renderHTML = (req, res) => {
      renderHTMLString(serverRender.routes, req, (error, redirectLocation, data) => {
        if (error) {
          res.status(error.message === 'Not found' ? 404 : 500).send(error.message);
        } else if (redirectLocation) {
          res.redirect(302, `${redirectLocation.pathname}${redirectLocation.search}`);
        } else {
          res.set('X-Frame-Options', 'sameorigin');
          res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
          res.render('index', {
            content: data.content,
            data: JSON.stringify(data.data).replace(/\//g, '\\/')
          });
        }
      });
    };

    app.get('*', RubixAssetMiddleware('ltr'), (req, res, next) => {
      _cookies = req.cookies;

      // Reset our assets pathways
      if (process.env.NODE_ENV === 'production') {
        const version = require('./version.json').v;
        res.locals.app_stylesheets = res.locals.app_stylesheets.replace(
          '/css',
          `/admin/css/${version}/`
        );
        res.locals.app_scripts = res.locals.app_scripts.replace(/\/js\//g, `/admin/js/${version}/`);
      }

      let envVars = configMgr.getClientEnvVars();
      const APM_VARS = {
        'ELASTIC_APM_SERVICE_NAME': process.env['ELASTIC_APM_SERVICE_NAME'] || 'edc-cms',
        'ELASTIC_APM_SERVER_URL': process.env['ELASTIC_APM_SERVER_URL'] || 'http://localhost:8200',
      };

      // Sidebar Config Params
      try {
        if (
          app.config['HIDDEN_SIDEBAR']
            .split(',')
            .indexOf(req.hostname.split('.')[0].toLowerCase()) > -1
        ) {
          Object.assign(envVars, { SHOW_SIDEBAR: true });
        }
      } catch (e) {}
      
      res.locals.app_vars = JSON.stringify(Object.assign(envVars, APM_VARS));
      res.locals.cspPolicy = cspPolicy;
      renderHTML(req, res);
    });

    app.listen(port, () => {
      console.log(`Admin Console app running at http://${host}.lvh.me:${port}/admin`);
    });
  } catch (error) {
    console.log(error);
  }
})();
