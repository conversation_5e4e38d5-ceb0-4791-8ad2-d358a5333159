import moment from 'moment';
import {
  Assessment,
  Badge,
  Certificate,
  Experience,
  Patent,
  SkillRaw,
  OverviewSectionsInfo,
  PatentFormData,
  AttachmentRaw,
  Activity,
  Certifications,
  Badges,
} from './types';
import addSecurity from 'edc-web-sdk/helpers/filestackSecurity';
import { FILESTACK_DEFAULT_EXPIRY } from 'edc-web-sdk/config/envConstants';
import { formatData } from '@utils/utils';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';
import { Permissions } from '@utils/checkPermissions';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';

export const mapBadges = (response: any | undefined, sections: OverviewSectionsInfo): Badges => {
  const { badges, totalBadges } = response || {};
  const badgeList = badges?.map((badge: any): Badge => ({
    id: badge.id,
    identifier: badge?.identifier,
    title: badge.badgeName,
    description: badge.description,
    attachment: badge?.credential,
    skills: badge?.skills,
    level: {
      value: badge.proficiencyLevel,
      label: badge.translatedLevel
    },
    issuer: badge.issuer,
    url: badge?.badgeUrl?.trim(),
    badgeId: badge?.badgeId,
    issueDate: badge?.issueDate,
    expiryDate: badge?.expiryDate,
    creationDate: badge.createdAt,
    canManage: !badge.verified,
    badgeUserId: badge?.badgeUserId
  })).sort((a: Badge, b: Badge) => {
    if (a.issueDate && b.issueDate) {
      return moment(b.issueDate).diff(moment(a.issueDate));
    } else if (a.issueDate) {
      return -1;
    } else if (b.issueDate) {
      return 1;
    } else {
      return moment(b.creationDate).diff(moment(a.creationDate));
    }
  }) ?? [];
  return {
      list: badgeList,
      total: totalBadges,
      visible: sections.badges.isVisible,
      private: sections.badges.isPrivate,
      canAdd: sections.badges.canAdd,
      label: sections.badges.label
  }
}

export const mapBadgeAfterSave = (payload: any): Badge => {
  return {
    id: payload.id,
    title: payload.credential_name,
    description: payload.description,
    attachment: prepareAttachmentAfterSave(payload?.credential?.[0]) ?? undefined,
    skills: payload?.skills?.map((skill: any): SkillRaw => ({
      topicId: skill.topic_id,
      topicLabel: skill.topic_label,
      topicName: skill.topic_name,
    })),
    level: {
      value: payload.proficiency_level,
      label: payload.translated_level,
    },
    issuer: payload.issuer,
    url: payload.credential_url?.trim(),
    badgeId: payload.credential_id,
    issueDate: payload.issue_date,
    expiryDate: payload.expiry_date,
    creationDate: payload.created_at,
    canManage: !payload.verified,
    badgeUserId: payload?.user_id
  }
}

export const mapCertifications = (response: any | undefined, sections: OverviewSectionsInfo): Certifications => {
  const {
    certifications,
    totalCertifications,
    assessments,
    totalAssessments,
    patents,
    totalPatents
  } = response || {};

  const mappedCertifications = certifications?.map((certification: any): Certificate => ({
    id: certification.id,
    type: "certificate",
    title: certification.certificationName,
    description: certification.description,
    attachment: certification?.credential,
    skills: certification?.skills,
    level: {
      value: certification.proficiencyLevel,
      label: certification.translatedLevel
    },
    issuer: certification.issuer,
    url: certification.certificationUrl?.trim(),
    certificateId: certification.certificationId,
    issueDate: certification.issueDate,
    expiryDate: certification.expiryDate,
    creationDate: certification.createdAt,
    canManage: !certification.verified
  })) ?? [];

  const mappedPatents = patents?.map((patent: any): Patent => ({
    id: patent.id,
    type: "patent",
    title: patent.title,
    description: patent.description,
    attachment: patent?.credential,
    nameOfInventors: patent?.nameOfInventor,
    awardedBy: patent.awardedBy,
    url: patent.patentUrl?.trim(),
    applicationNumber: patent.applicationNumber,
    patentNumber: patent.patentNumber,
    dateOfPatent: patent.dateOfPatent,
    filedOn: patent.filedOn,
    expiryDate: patent.expiryDate,
    creationDate: patent.createdAt,
    canManage: !patent.verified
  })) ?? []

  const mappedAssessments = assessments?.map((assessment: any): Assessment => ({
    id: assessment.id,
    type: "assessment",
    title: assessment.assessmentName,
    description: assessment.description,
    attachment: assessment?.credential,
    skills: assessment?.skills,
    level: {
      value: assessment.proficiencyLevel,
      label: assessment.translatedLevel
    },
    issuer: assessment?.issuer,
    score: assessment?.score,
    url: assessment.reportUrl?.trim(),
    issueDate: assessment.assessmentDate,
    creationDate: assessment.creationDate,
    canManage: !assessment.verified
  })) ?? [];

  return {
    certificateList: mappedCertifications,
    certificatesTotal: totalCertifications || 0,
    certificateAddAvailable: sections.certifications.canAdd,
    certificatesPrivate: sections.certifications.isPrivate,
    patentList: mappedPatents,
    patentsTotal: totalPatents || 0,
    patentAddAvailable: sections.patents.canAdd,
    patentsPrivate: sections.patents.isPrivate,
    assessmentList: mappedAssessments,
    assessmentsTotal: totalAssessments || 0,
    assessmentsPrivate: sections.assessments.isPrivate,
    assessmentAddAvailable: sections.assessments.canAdd,
    visible: sections.certifications.isVisible || sections.assessments.isVisible || sections.patents.isVisible,
    label: sections.certifications.label
  }
}

export const mapCertificateAfterSave = (payload: any): Certificate => {
  return {
    id: payload.id,
    type: "certificate",
    title: payload.credential_name,
    description: payload.description,
    attachment: prepareAttachmentAfterSave(payload?.credential?.[0]) ?? undefined,
    skills: payload?.skills?.map((skill: any): SkillRaw => ({
      topicId: skill.topic_id,
      topicLabel: skill.topic_label,
      topicName: skill.topic_name,
    })),
    level: {
      value: payload.proficiency_level,
      label: payload.translated_level,
    },
    issuer: payload.issuer,
    url: payload.credential_url?.trim(),
    certificateId: payload.credential_id,
    issueDate: payload.issue_date,
    expiryDate: payload.expiry_date,
    creationDate: payload.created_at,
    canManage: !payload.verified
  }
}

const prepareAttachmentAfterSave = (attachmentPayload: any): AttachmentRaw => {
  const isLxMediaEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);

  if (isLxMediaEnabled && attachmentPayload) {
    return {
      contentType: attachmentPayload.content_type,
      filename: attachmentPayload.filename,
      size: attachmentPayload.size,
      handle: attachmentPayload.handle,
      url: attachmentPayload.url,
      previewUrl: attachmentPayload.previewUrl
    }
  }

  if(attachmentPayload) {
    return {
      filename: attachmentPayload.filename,
      handle: attachmentPayload.handle,
      mimetype: attachmentPayload.mimetype,
      size: attachmentPayload.size,
      url: addSecurity(attachmentPayload.url, FILESTACK_DEFAULT_EXPIRY, window.__ED__.id),
    }
  }
  return undefined;
}

export const mapAssessmentAfterSave = (payload: any): Assessment => {
  return {
    id: payload.id,
    type: "assessment",
    title: payload.credential_name,
    description: payload.description,
    attachment: prepareAttachmentAfterSave(payload?.credential?.[0]) ?? undefined,
    skills: payload?.skills?.map((skill: any): SkillRaw => ({
      topicId: skill.topic_id,
      topicLabel: skill.topic_label,
      topicName: skill.topic_name,
    })),
    level: {
      value: payload.proficiency_level,
      label: payload.translated_level,
    },
    score: payload.score,
    issuer: payload.issuer,
    url: payload.credential_url?.trim(),
    issueDate: payload.issue_date,
    creationDate: payload.created_at,
    canManage: !payload.verified
  }
}

export const mapPatentAfterSave = (payload: any, newPatent: PatentFormData): Patent => {
  return {
    applicationNumber: newPatent.applicationNumber,
    awardedBy: newPatent.awardedBy,
    dateOfPatent: newPatent.dateOfPatent?.toString(),
    expiryDate: newPatent.expiryDate?.toString(),
    filedOn: newPatent.filedOn?.toString(),
    nameOfInventors: newPatent.nameOfInventors,
    patentNumber: newPatent.patentNumber,
    id: payload.id,
    type: "assessment",
    title: payload.credential_name,
    description: payload.description,
    attachment: prepareAttachmentAfterSave(payload?.credential?.[0]) ?? undefined,
    url: payload.credential_url?.trim(),
    creationDate: payload.created_at,
    canManage: !payload.verified
  }
}

export const mapWorkExperiences = (experiences: any): Array<Experience> => {
  const removeTimeFromTheDate = (date: string) => {
    if (!date) {
      return undefined;
    }
    if (typeof date === 'string') {
      const splitDate = date.split('T');
      if (splitDate.length) {
        return splitDate[0];
      }
    }
    return date;
  }

  const isWorkHistoryDisabled = getConfig('automatic_profile_change_work_history_disabled');
 experiences = experiences && isWorkHistoryDisabled ? experiences.filter(item => item.event_type === "workHistory") : experiences;

  return experiences?.map((experience: any): Experience => {
    return ({
      id: experience.uuid,
      title: experience.title,
      company: experience.company,
      startDate: removeTimeFromTheDate(experience.start_date),
      endDate: removeTimeFromTheDate(experience?.end_date) ?? undefined,
      role: experience?.job_role_detail?.id ? {
        id: experience.job_role_detail.id,
        label: experience.job_role_detail.title_text,
      } : undefined,
      description: experience.description,
    })
  }).sort((a: Experience, b: Experience) => {
      if ((a.endDate === undefined && b.endDate === undefined) || a.endDate === b.endDate) {
        return moment(b.startDate).diff(moment(a.startDate));
      } else if (a.endDate === undefined) {
        return -1;
      } else if (b.endDate === undefined) {
        return 1;
      } else {
        return moment(b.endDate).diff(moment(a.endDate));
      }
    }) ?? []
}

export const mapInterestsGroups = (groups: any) => {
  return groups?.teams?.map((group: any) => ({
    id: group.id,
    name: group.name,
    membersCount: group.membersCount,
    img: group?.imageUrls?.small
  })) ?? []
}

export const mapInterestsChannels = (channels: any) => {
  return channels?.channels?.map((channel: any) => ({
    id: channel.id,
    name: channel.label,
    followersCount: channel.followersCount,
    img: channel?.profileImageUrls?.small_url || channel?.bannerImageUrls?.small_url,
  })) ?? []
}

export const mapActivity = (activities: any): Array<Activity> => {
  const formattedActivities = activities ? formatData(activities?.activityStreams || activities?.activity_streams) : [];
  return formattedActivities?.map((activity: any): Activity => ({
      action: activity.action,
      createdAt: activity.createdAt,
      snippet: activity.snippet,
      linkUrl: activity.linkUrl,
      linkPrefix: activity.linkPrefix
    }))
}

export const mapContentList = (content: any) => {
  const canManageCard = Permissions.has("MANAGE_CARD");
  return content?.cards?.map(
    (content: any) => ({
      ...content,
      id: content.id,
      title: content.cardTitle,
      description: content.cardMessage,
      readableCardType: content.readableCardType,
      cardType: content.cardType,
      attachment: content.filestack,
      url: content.resource?.url,
      creationDate: content.createdAt,
      canManage: canManageCard
    })
  );
}
