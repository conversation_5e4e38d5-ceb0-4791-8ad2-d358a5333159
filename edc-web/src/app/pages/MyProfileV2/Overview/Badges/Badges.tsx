import React from 'react';
import { Link } from 'react-router-dom';
import { translate } from '../../utils';
import { useOverviewTabData } from '../../ProfileProvider';
import Card from 'centralized-design-system/src/Card/CardContainer';
import { PlusButton } from '../../Components/ActionButtons';
import BadgeTile from './BadgesTile';
import "./Badges.scss";
import NavigationLink from '../../Components/NavigationLink';
import CardHeader from '../../Components/Card/CardHeader';
import LD from '../../../../containers/LDStore';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';

const MAX_NUMBER_OF_BADGES = 2;

const Badges = () => {
  const { state: { badges, selfView }, dispatch } = useOverviewTabData();

  const badgesButtons = [
    badges.canAdd && (<PlusButton
          onClick={() => dispatch({ type: "OPEN_MODAL", modal: "badges", action: "add" }) }
          ariaLabel={translate("AriaLabelAddIcon")}
        />),
    selfView && (<Link to="badges">
      <span className="icon-pencil" role="button" aria-label={translate("AriaLabelEditIcon")}></span>
    </Link>)
  ].filter(Boolean);

  const isBadgesSectionVisible = badges.visible && badges.list.length > 0;

  return isBadgesSectionVisible &&
    <Card additionalClasses={["badges_card"]}>
      <CardHeader
        cardTitle={badges.label}
        showPrivateToYouTag={selfView && badges.private}
        showActionButtons={badges.canAdd || selfView}
        buttons={badgesButtons}
      />
      <div className="badges_tiles-container">
        {badges?.list.slice(0, MAX_NUMBER_OF_BADGES).map(badge => (
          <BadgeTile
            key={badge.id}
            badge={badge}
            showEditButton={false}
          />
        ))}
      </div>
      <NavigationLink to="badges" >
        {translate("ShowAllLink", { total: badges.total.toString()})}
      </NavigationLink>
    </Card>;
};

export default Badges;
