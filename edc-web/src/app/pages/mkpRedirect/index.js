import React, { useEffect } from 'react';
import { bool } from 'prop-types';
import { allowedDomains, allowedPaths, dangerousPatterns } from './constants';

const MkpRedirect = () => {
  useEffect(() => {
    try {
      const currentUrl = window.location.href;
      const url = new URL(currentUrl);
      const redirectUrl = url.searchParams.get('redirect_url');

      if (!redirectUrl) {
        console.error('No redirect_url found in the URL.');
        safeRedirect('/');
        return;
      }

      if (isValidRedirectUrl(redirectUrl)) {
        safeRedirect(redirectUrl);
      } else {
        console.error('Invalid redirect URL detected:', redirectUrl);
        safeRedirect('/');
      }
    } catch (error) {
      console.error('Error processing redirect:', error);
      safeRedirect('/');
    }
  }, []);

  // Safely perform redirects
  const safeRedirect = url => {
    // Final safety check before redirect
    if (typeof url === 'string' && isValidRedirectUrl(url)) {
      window.location.href = url;
    } else {
      window.location.href = '/';
    }
  };

  // Comprehensive URL validation function
  const isValidRedirectUrl = url => {
    if (!url || typeof url !== 'string') {
      return false;
    }

    // Trim the URL to remove any leading/trailing whitespace
    const trimmedUrl = url.trim();

    if (dangerousPatterns.some(pattern => trimmedUrl.toLowerCase().includes(pattern))) {
      return false;
    }

    try {
      const parsedUrl = new URL(trimmedUrl);

      // Only allow HTTPS protocol
      if (parsedUrl.protocol !== 'https:') {
        console.warn('URL validation failed: Non-HTTPS protocol');
        return false;
      }

      // Prevent credential embedding
      if (parsedUrl.username || parsedUrl.password) {
        console.warn('URL validation failed: Contains credentials');
        return false;
      }

      // Special case for payments subdomains
      const isPaymentsSubdomain =
        parsedUrl.hostname.startsWith('payments.') &&
        allowedDomains.some(domain => parsedUrl.hostname.endsWith(domain));

      // Check if hostname matches any *.allowedDomain pattern
      const isAllowedDomain = allowedDomains.some(domain => parsedUrl.hostname.endsWith(domain));

      // For payments subdomains, any path is allowed
      // For other domains, check if path starts with any allowed path
      const hasAllowedPath =
        isPaymentsSubdomain || allowedPaths.some(path => parsedUrl.pathname.startsWith(path));

      if (!(isAllowedDomain && hasAllowedPath)) {
        console.warn(
          'URL validation failed: Untrusted domain or path',
          parsedUrl.hostname,
          parsedUrl.pathname
        );
        return false;
      }

      // Check for suspicious query parameters or fragments that might contain XSS
      if (
        dangerousPatterns.some(
          pattern =>
            parsedUrl.search.toLowerCase().includes(pattern) ||
            parsedUrl.hash.toLowerCase().includes(pattern)
        )
      ) {
        console.warn('URL validation failed: Suspicious query parameters or fragment');
        return false;
      }

      // Validate URL path for directory traversal attempts
      if (parsedUrl.pathname.includes('..')) {
        console.warn('URL validation failed: Directory traversal attempt');
        return false;
      }

      // Check URL length to prevent DoS attacks
      const fullUrl = parsedUrl.toString();
      if (fullUrl.length > 2048) {
        console.warn('URL validation failed: URL too long');
        return false;
      }

      return true;
    } catch (e) {
      // If URL parsing fails, it's not a valid URL
      console.warn('URL validation failed: Invalid URL format', e.message);
      return false;
    }
  };

  return <></>;
};

MkpRedirect.propTypes = {
  edcastPricing: bool
};

export default MkpRedirect;
