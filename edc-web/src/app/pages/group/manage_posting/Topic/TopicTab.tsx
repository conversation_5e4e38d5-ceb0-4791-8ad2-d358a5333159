import React, { useEffect, useRef, useState } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import ManageTable from '../Components/Table/ManageTable';
import RowActions from '../Components/RowActions';
import ConfirmationModal from '../Components/ConfirmationModal';
import {
  SortOrder,
  TableHeaderElement,
  Topic,
  TopicFormData,
  TopicsFilter,
  TopicSortOptionId
} from '../types';
import { translate } from '../utils';
import { useGroupManagePostsContext } from '../GroupManagePostsProvider';
import TopicModal from './TopicModal';
import { useTopics } from './useTopics';
import { TOPIC_ELEMENTS_PER_PAGE } from './api';
import { getRoleTranslation } from '@pages/group/user_management/util';

// todo: to reuse in the future
// const topicNavigationElement: Array<{ value: TopicsFilter, label: string }> = [
//   {value: "all", label: translate("AllTopicsNavigation"),},
//   {value: "archived", label: translate("ArchivedNavigation"),},
// ];

const topicHeaders: Array<TableHeaderElement> = [
  { label: translate("TopicColumnName"), columnId: 'name', sortable: false },
  { label: translate("CreatedColumnName"), columnId: 'created', sortable: true, isDate: true },
  { label: translate("LastUpdateColumnName"), columnId: 'lastUpdate', sortable: true, isDate: true },
  { label: translate("PostsColumnName"), columnId: 'posts', sortable: true, isNode: true },
  { label: translate("ActionsColumnName"), columnId: 'actions', sortable: false, isNode: true }
];

const TopicTab = () => {
  const isFirstRender = useRef(true);
  const { groupManagePosts: { groupId }, openSnackbar, topicPostsCountClicked } = useGroupManagePostsContext();
  const { topics, loadTopics, removeTopic, saveTopic } = useTopics(groupId);

  const [openTopicForm, setOpenTopicForm] = useState(false);
  const [editedTopic, setEditedTopic] = useState<Topic | null>(null);
  const [topicIdToDelete, setTopicIdToDelete] = useState<number | null>(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // todo: to reuse in the future
  const [filter, setFilter] = useState<TopicsFilter>('all');
  const [sort, setSort] = useState<TopicSortOptionId>('creation_date_desc');
  const [offset, setOffset] = useState<number>(0);

  useEffect(() => {
    loadTopics(filter, sort, offset);
  }, [filter, sort, offset]);

  const focusOnRowActionsButtonWhenDeleteConfirmationClosed = () => {
    if(!showDeleteConfirmation && topicIdToDelete) {
      document.getElementById(`row-actions-${topicIdToDelete}`)?.focus();
      setTopicIdToDelete(null);
    }
  }

  useEffect(focusOnRowActionsButtonWhenDeleteConfirmationClosed, [showDeleteConfirmation]);

  const focusOnButtonWhenTopicFormModalClosed = () => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    if(openTopicForm) {
      return;
    }
    if(editedTopic) {
      document.getElementById(`row-actions-${editedTopic.id}`)?.focus();
      setEditedTopic(null);
    } else {
      document.getElementById('button-add-topic')?.focus();
    }
  }

  useEffect(focusOnButtonWhenTopicFormModalClosed, [openTopicForm])

  const onTopicModalClose = () => {
    setOpenTopicForm(false);
  }

  const handleCloseConfirmationModal = () => {
    setShowDeleteConfirmation(false);
  }

  const openTopicModalForEdit = (topic: Topic) => {
    setEditedTopic(topic);
    setOpenTopicForm(true);
  }

  const openDeleteConfirmationModal = (topicId: number) => {
    setTopicIdToDelete(topicId);
    setShowDeleteConfirmation(true);
  }

  const topicData = topics.data.map(topic => ({
    ...topic,
    posts: (<button
        className="manage-posts-link"
        aria-label={translatr('web.group.main', 'PostsCountAriaLabel', {
          topicName: topic.name
        })}
        onClick={() => topicPostsCountClicked({ selectedTopicId: topic.id })}
      >
        {topic.posts}
      </button>
    ),
    actions: <RowActions
      id={`row-actions-${topic.id}`}
      dropdownOptions={[
        { id: 'edit', label: translate("EditTopicAction"), onClick: () => openTopicModalForEdit(topic) },
        { id: 'delete', label: translate("DeleteTopicAction"), onClick: () => openDeleteConfirmationModal(topic?.id)}
      ]}
    />
  }));

  const sortTopics = (columnName: string, sortOrder: SortOrder) => {
    const sort = {
      created: `creation_date_${sortOrder.toLowerCase()}`,
      lastUpdate: `last_update_${sortOrder.toLowerCase()}`,
      posts: `posts_count_${sortOrder.toLowerCase()}`
    }[columnName] as TopicSortOptionId || 'creation_date_desc';
    setSort(sort);
  }

  const handlePageChange = (pageNumber: number) => {
    const newOffset = (pageNumber - 1) * TOPIC_ELEMENTS_PER_PAGE;
    if (offset != newOffset) {
      setOffset(newOffset)
    }
  }

  const handleDeleteTopic = () => {
    removeTopic(topicIdToDelete)
      .then(() => {
        openSnackbar(translate('TopicDeletedSuccessMessage'), 'success');
        setTopicIdToDelete(null);
        handleCloseConfirmationModal();
      })
      .catch((error) => {
        const errorCode = error.response?.body?.errorCode;
        if (errorCode === 'LAST_ACTIVE_TOPIC' || errorCode === 'TOPIC_CONTAINS_POSTS') {
          const messageKey = errorCode === 'LAST_ACTIVE_TOPIC'
            ? 'LastActiveTopicErrorMessage'
            : 'TopicContainsPostsErrorMessage';
          openSnackbar(translate(messageKey), 'error');
          setTopicIdToDelete(null);
          handleCloseConfirmationModal();
          return;
        }
        openSnackbar(translate('TopicDeletedErroredMessage'), 'error');
      });
  };

  const onSaveTopic = async (formData: TopicFormData) =>
    saveTopic(formData, editedTopic?.id)
      .then(() => {
        if (editedTopic) {
          openSnackbar(translate('TopicFormUpdatingSuccessfullyNotification'), 'success');
        } else {
          openSnackbar(translate('TopicFormSavingSuccessfullyNotification'), 'success');
        }
        onTopicModalClose();
      })
      .catch((e) => {
        if (e.response?.status === 422 && e.response?.body?.message?.includes('Forbidden content in the name field')) {
          openSnackbar(translate('TopicFormSavingFailureEmbargoNotification'), 'error')
        } else {
          openSnackbar(translate('TopicFormSavingFailureNotification'), 'error')
        }
      });

  // todo: to reuse in the future
  // const topicTabFilters = [{
  //   id: "topic-state",
  //   value: topicNavigationElement.find(element => element.value === queryData.filter)?.value,
  //   items: topicNavigationElement,
  //   onSelect: ({ value: filter }) => updateQueryData({ filter })
  // }]

  return (
    <div>
      <ManageTable
        filters={[]}
        buttons={[{
          id: `button-add-topic`,
          label: translate("ButtonAddTopicLabel"),
          onClick: () => setOpenTopicForm(true)
        }]}
        headers={topicHeaders}
        data={topicData}
        total={topics.count}
        elementsPerPage={TOPIC_ELEMENTS_PER_PAGE}
        onPageChange={(pageNumber) => handlePageChange(pageNumber)}
        showError={topics.error}
        loading={topics.loading}
        onSort={sortTopics}
        defaultSortKey={"created"}
      />
      {openTopicForm && <TopicModal
        handleClose={onTopicModalClose}
        editedTopic={editedTopic}
        onSaveTopic={onSaveTopic}
      />}
      {showDeleteConfirmation &&
        <ConfirmationModal
          title={translate("DeleteTopicConfirmationModalTitle")}
          description={translate("DeleteTopicConfirmationModalDescription")}
          handleClose={handleCloseConfirmationModal}
          handleAction={handleDeleteTopic}
          actionLabel={translatr('web.common.main', 'Delete')}
          actionVariant={'caution'}
        />
      }
    </div>
  );
};

export default TopicTab;
