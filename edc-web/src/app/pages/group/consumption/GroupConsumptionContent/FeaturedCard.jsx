import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { getTranslatedSkillLevelLabel } from 'centralized-design-system/src/Utils/proficiencyLevels';
import { FILESTACK_DEFAULT_EXPIRY } from 'edc-web-sdk/config/envConstants';
import { fsDecrypt } from 'edc-web-sdk/helpers/filestackSecurity';

import './FeaturedCard.scss';

import { truncateText, getFilestackUrlWithSecurity } from '@utils/utils';
import { getContentTypeLabel } from '@utils/getContentTypeLabel';
import SvgImageResized from 'centralized-design-system/src/SvgImageResized';
import fetchCardType from '@components/cardStandardization/utils/fetchCardType';
import { getCardOrRandomImage, getFeaturedCardTitle } from '@utils/smartCardUtils';
import linkPrefix from '@utils/linkPrefix';
import Tooltip from 'centralized-design-system/src/Tooltip';

// Wrapper component to prevent clicks on tooltip while allowing hover
const HoverOnlyTooltip = ({ children, allowCheckboxToggle = false, ...tooltipProps }) => {
  const handleClick = (e) => {
    // Check if we're inside a checkbox context by looking for a parent label element
    const isInCheckboxContext = e.target.closest('label.checkbox') !== null;

    if (allowCheckboxToggle && isInCheckboxContext) {
      // In checkbox context, we need to manually trigger the checkbox
      // since the link's pointer-events: none prevents normal label behavior

      // Find the checkbox input element
      const checkboxLabel = e.target.closest('label.checkbox');
      const checkboxInput = checkboxLabel?.querySelector('input[type="checkbox"]');

      if (checkboxInput) {
        // Prevent the link navigation
        e.preventDefault();
        e.stopPropagation();

        // Manually trigger the checkbox change by simulating a click on the input
        // This is more reliable than manually setting checked and dispatching events
        checkboxInput.click();

        return false;
      }
    }

    // For non-checkbox contexts, prevent all click behavior
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    return false;
  };

  const handleMouseDown = (e) => {
    // Check if we're inside a checkbox context
    const isInCheckboxContext = e.target.closest('label.checkbox') !== null;

    if (allowCheckboxToggle && isInCheckboxContext) {
      // In checkbox context, prevent mousedown from interfering with our custom click handling
      e.preventDefault();
      return false;
    }

    // For non-checkbox contexts, prevent mousedown
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    return false;
  };

  const handleKeyDown = (e) => {
    // Handle Enter and Space keys for checkbox toggle when in checkbox context
    if (allowCheckboxToggle && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      handleClick(e);
    }
  };

  return (
    <Tooltip {...tooltipProps}>
      <div
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onKeyDown={handleKeyDown}
        style={{ cursor: allowCheckboxToggle ? 'pointer' : 'default' }}
        role={allowCheckboxToggle ? 'button' : undefined}
        tabIndex={allowCheckboxToggle ? 0 : undefined}
        aria-label={allowCheckboxToggle ? 'Toggle checkbox selection' : undefined}
      >
        {children}
      </div>
    </Tooltip>
  );
};

const FeaturedCard = ({ card, filestackUrlExpireAfterSeconds, currentUserId, allowCheckboxToggle = false }) => {
  const { readableCardType, skillLevel, translatedSkillLevel } = card;
  const title = getFeaturedCardTitle(card);
  const cardType = fetchCardType(card);
  const { img, mimeType } = getCardOrRandomImage(card, cardType, true, currentUserId);
  const expireAfter = filestackUrlExpireAfterSeconds || FILESTACK_DEFAULT_EXPIRY;
  const filestackDetails = card.filestack?.[0] || {};
  const { handle, url } = filestackDetails;
  const cardUrl = `/${linkPrefix(card.cardType)}/${card.slug}`;

  const filestackUrlParams = {
    url,
    expireAfter,
    currentUserId,
    handle
  };
  // Determines whether to show the thumbnail based on the presence of an thumbnail
  const showFile = Boolean(img || (card.filestack.length && mimeType !== 'image/jpeg'));
  /**
   * Selects and returns the appropriate component based on the MIME type of the card.
   *
   * @return {JSX.Element} The selected component to render.
   */
  const selectComponentByMimeType = () => {
    const renderSvgImage = imgUrl => {
      const transform = 'output=format:jpg,page:1/';
      const cache = 'cache=e:31536000/'; //1 year === 31536000 seconds
      return (
        <div className="blurred-thumbnail">
          <div className="default card-std-ie-image-fix">
            <SvgImageResized
              xlinkHref={imgUrl}
              x="-30%"
              y="-30%"
              width="160%"
              height="160%"
              transform={transform}
              cache={cache}
              loading="lazy"
            />
          </div>
        </div>
      );
    };

    switch (mimeType) {
      case 'application':
        const decrypted = fsDecrypt(getFilestackUrlWithSecurity(filestackUrlParams));
        const fileUrl = card.media?.url || decrypted?.filestack_url;
        return renderSvgImage(fileUrl);
      case 'audio':
        return (
          <div className="blurred-thumbnail make-center font-size-30">
            <i className="icon-audio-thumbnail card-metadata-icon"></i>
          </div>
        );
      default:
        return renderSvgImage(img);
    }
  };

  const Component = showFile ? selectComponentByMimeType() : null;

  return (
    <li>
      <a
        href={cardUrl}
        className="featured__container--card justflex"
        target="_blank"
        role="button"
        rel="noopener noreferrer"
      >
        <div className="img-container relative overflow-hidden">{Component}</div>
        <div className="info__container flex-1 supporting-text">
        <HoverOnlyTooltip
          message={title}
          isHtmlIncluded={true}
          pos="top"
          customClass='featured-card-tooltip'
          allowCheckboxToggle={allowCheckboxToggle}
        >
          <div className="info__container--title overflow-hidden">{title}</div>
        </HoverOnlyTooltip>
          <div className="info__container--details">
            <span className="type">
              {getContentTypeLabel(readableCardType) || readableCardType}
            </span>{' '}
            {skillLevel && (
              <>
                {readableCardType && '|'}{' '}
                <span className="level">
                  {truncateText(
                    getTranslatedSkillLevelLabel(translatedSkillLevel || skillLevel),
                    10,
                    '...'
                  )}
                </span>
              </>
            )}
          </div>
        </div>
      </a>
    </li>
  );
};

FeaturedCard.propTypes = {
  card: PropTypes.object,
  filestackUrlExpireAfterSeconds: PropTypes.number,
  currentUserId: PropTypes.string,
  allowCheckboxToggle: PropTypes.bool
};

const mapStateToProps = state => {
  const { filestack_url_expire_after_seconds: filestackUrlExpireAfterSeconds } = state.team.get(
    'config'
  );

  return {
    filestackUrlExpireAfterSeconds,
    currentUserId: state.currentUser.get('id')
  };
};

export default connect(mapStateToProps)(FeaturedCard);
