import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import RichTextEditor from 'react-rte';
import unescape from 'lodash/unescape';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import Tooltip from 'centralized-design-system/src/Tooltip';

class RichTextReadOnly extends Component {
  constructor(props, context) {
    super(props, context);
    this.state = {};
    this.styles = {};
  }

  render() {
    const { team, text, tagType, truncate, fullTitle } = this.props;
    const config = team && team.config && team.config.link_blank;
    if (!text) {
      return <div />;
    }

    let markup = 'markdown';
    let renderText;

    if (tagType) {
      markup = 'html';
      const textWithTag = `<${tagType}> ${text} </${tagType}>`;
      renderText = textWithTag;
    } else {
      renderText = text;
    }

    let textValue = RichTextEditor.createValueFromString(renderText, markup)
      .toString('html')
      .replace(/href="https:([/]*)/g, 'href="')
      .replace(/href="http:([/]*)/g, 'href="')
      .replace(/href="/g, 'href="//');

    if (truncate && textValue?.length > 65) {
      textValue = textValue.substring(0, 62);
      textValue = `${textValue}...`;
    }

    return (
      fullTitle ? (
        <Tooltip
          message={fullTitle}
          isHtmlIncluded={true}
          pos="top"
          id="card-title-tooltip"
        >
          <div
            className="rich-text-read-only"
            dangerouslySetInnerHTML={{
              __html: config
                ? safeRender(
                    unescape(textValue.replace(/<a /g, '<a target="_blank" rel="noopener noreferrer"'))
                  )
                : safeRender(unescape(textValue))
            }}
          />
        </Tooltip>
      ) : (
        <div
          className="rich-text-read-only"
          dangerouslySetInnerHTML={{
            __html: config
              ? safeRender(
                  unescape(textValue.replace(/<a /g, '<a target="_blank" rel="noopener noreferrer"'))
                )
              : safeRender(unescape(textValue))
          }}
        />
      )
    );
  }
}

RichTextReadOnly.propTypes = {
  text: PropTypes.string,
  team: PropTypes.object,
  truncate: PropTypes.bool,
  tagType: PropTypes.string
};

function mapStoreStateToProps(state) {
  return {
    team: state.team.toJS()
  };
}

export default connect(mapStoreStateToProps)(RichTextReadOnly);
