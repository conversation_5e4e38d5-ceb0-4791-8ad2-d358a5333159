import { hasPreActivity, isLmsPaidContent, isPaymentCompleted, isWorkflowPresentForLmsPaidContent } from '@utils/utils';

type Card = {
  lmsWorkflows?: object;
  launchInline?: boolean;
  isMfeOnePlayerEnabled?: boolean;
  transcriptData?: {
    contentStatus?: string;
  };
};

export const shouldDisplayLaunchContentBtn = (card: Card): boolean => {
  const contentStatus =  card?.transcriptData?.contentStatus ?? '';
  const meetsPaymentRequirements = isLmsPaidContent(card) ? isPaymentCompleted(contentStatus) : true;

  return (
    card?.hasOwnProperty('lmsWorkflows') && // presence of this key indicates lms_workflow_enabled is set to true
    !hasPreActivity(card) && // There are no prerequisite/prework/approval pending
    meetsPaymentRequirements && // if content is paid, payment is completed
    !card?.launchInline && // launchInline is false and oneplayer is enabled
    card?.isMfeOnePlayerEnabled &&
    !isWorkflowPresentForLmsPaidContent(card) // Hide btn if workflow is present for lms paid content
  );
};
