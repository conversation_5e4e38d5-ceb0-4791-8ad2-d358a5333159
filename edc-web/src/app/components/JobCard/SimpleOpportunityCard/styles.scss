.soc-card {
  background-color: var(--ed-card-bg-color);
  border: var(--ed-card-border-size) solid var(--ed-card-border-color);
  border-radius: var(--ed-card-border-radius);
  padding: var(--ed-spacing-4xs);
  width: 100%;

  &-meta {
    display: flex;
    flex-direction: row;
    gap: var(--ed-spacing-lg);
    :hover {
      cursor: pointer;
    }
  }
  & &-content {
    flex: 1;
    width: 80%;

    .soc-card-title {
      font-size: var(--ed-font-size-base);
      font-weight: var(--ed-font-weight-black);
      h3 {
        margin-bottom: 0;
        margin-right: var(--ed-spacing-2xs);
        display: inline-flex;
      }
    }
    .soc-card-location {
      font-size: var(--ed-font-size-sm);
      display: flex;
      gap: var(--ed-spacing-3xs);
      max-width: 830px;
      & > div {
        width: auto;
        display: flex;
        gap: var(--ed-spacing-3xs);
        button {
          text-wrap: nowrap;
        }
        .joblocation--tooltip-locations {
          display: block;
          .joblocation--view-more {
            white-space: nowrap;
          }
        }
      }
    }
    .soc-apply-date {
      font-size: var(--ed-font-size-sm);
    }
  }
  & .ed-dropdown {
    margin-right: 0;
    i {
      font-size: var(--ed-font-size-2xl);
    }
  }
}
