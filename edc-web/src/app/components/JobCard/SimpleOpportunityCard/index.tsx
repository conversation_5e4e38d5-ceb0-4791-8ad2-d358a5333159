import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import { ompLov, translatr } from 'centralized-design-system/src/Translatr';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import JobLocation from '../JobLocation';
import { LOCATION_USAGE_OPTIONS } from 'opportunity-marketplace/helpers';
import './styles.scss';
import DropdownActions from '@components/ProjectCard/shared/DropdownActions';
import { connect, useSelector } from 'react-redux';
import { openOMPShareModal } from 'opportunity-marketplace/shared/helpers/helpers';
import { FlagTag } from 'centralized-design-system/src/Tags';
import { Link } from 'centralized-design-system/src/Links';
import ConditionalWrapper from '@components/ConditionalWrapper';
import TextClamp from '@components/TextClamp';
import moment from 'moment';
import { DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT } from '@utils/constants';
import { isLovAvailableForOpportunityType } from '@pages/TalentMarketplace/util';
import { getViewAction } from '@components/cardStandardization/utils/shareOpportunityActions';
import { useNavigate } from 'react-router-dom';
import { ApplicationStatus } from '@pages/TalentMarketplace/shared/Types';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';

interface SimpleOpportunityCardProps {
    loading: boolean,
    type: string,
    id: string,
    title: string,
    mode: string,
    locations: any[],
    countries: any[],
    contractType: string,
    applyDate: string,
    openShareModal: (params: {type: string, id: string, openedFromHtmlElement: EventTarget}) => {},
    status: keyof typeof ApplicationStatus,
    applied: boolean
};

const SimpleOpportunityCard: React.FC<SimpleOpportunityCardProps> = ({
    loading,
    type,
    id,
    title,
    locations,
    countries,
    mode,
    applyDate,
    openShareModal,
    status,
    applied
}) => {
    const themeId = useSelector((state: any) => state?.theme?.get('themeId'));
    const cardId = `simple-opportunity-card-${id}`;
    const navigate = useNavigate();
    const viewAction = getViewAction(id, type, null, navigate);
    const sharedAction = {
        id: 'share-job',
        label: translatr('web.common.main', 'Share'),
        onClick: (e: React.MouseEvent<HTMLAnchorElement>) => openShareModal({ type: type, id, openedFromHtmlElement: e.target})
      };

      const navigateTo = (event: React.MouseEvent<HTMLAnchorElement | HTMLDivElement>) => {
        event.preventDefault();
        viewAction.onClick(event);
      };
      const onTitleKeyDown = (event: React.KeyboardEvent<HTMLAnchorElement>) => {
        if (event.key === 'Enter' || event.key === ' ') {
          viewAction.onClick(event);
        }
      };

    return (
        <article className={`soc-card card-${type}`} aria-labelledby={`${cardId}-title`} aria-describedby={`${cardId}-content`}>
          {(loading) ? (<>
            <Skeleton width="100%" height={22} />
            <Skeleton width="40%" count={2} />
          </>) : (
            <div className={`soc-card-meta`} onClick={navigateTo}>
                <section className="soc-card-content" id={`${cardId}-content`}>
                    <div className="soc-card-title">
                      <h3 id={`${cardId}-title`}>
                        <Link
                          color='secondary'
                          size={themeId === ThemeId.PICASSO ? 'large' : 'medium'}
                          to={`/career/detail/${type}/${id}`}
                          onClick={ev => ev.preventDefault()}
                          onKeyDown={onTitleKeyDown}
                          >
                            {title}
                        </Link>
                      </h3>
                      <FlagTag color='info'>{translatr('web.talentmarketplace.main', ApplicationStatus[status] || ApplicationStatus.SHOWED_INTEREST)}</FlagTag>
                    </div>
                    <div className="soc-card-location">
                      <span id={`location-title-${id}`} className="sr-only">{translatr('web.common.main', 'Location')}</span>
                      <ConditionalWrapper
                        condition={locations.length === 1}
                        wrapper={children => <TextClamp>{children}</TextClamp>}
                        defaultWrapper={children => <>{children}</>}
                      >
                        <JobLocation
                          locations={locations}
                          visibilityContext={LOCATION_USAGE_OPTIONS.JOB_VACANCY_CARD}
                          jobId={id}
                          countries={countries}
                          type={type}
                          lines={1}
                          titleId={`${cardId}-title`}
                          locationIconId={`location-title-${id}`}
                        />
                        {mode && isLovAvailableForOpportunityType('workplace_model', type, mode) && (
                          <>
                            <span> | </span>
                            <span dangerouslySetInnerHTML={{ __html: safeRender(ompLov('workplace_model', mode)) }} />
                          </>
                        )}
                      </ConditionalWrapper>
                    </div>
                    {applied && (<div className="soc-apply-date">{`${translatr('web.common.main', 'Applied')} ${moment(applyDate).format(DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT)}`}</div>)}
                </section>
                <DropdownActions
                  actions={[sharedAction]}
                  ariaLabel={translatr('web.common.main', 'MoreOpportunityActions', {
                    opportunity: title
                  })}
                />
            </div>
            )}
        </article>
    )
};

export default connect(null, dispatch => ({
  openShareModal: ({ id, type, openedFromHtmlElement }: {id: string, type: string, openedFromHtmlElement: EventTarget}) => dispatch(openOMPShareModal({ id, type, preSelectedIndividuals: [], openedFromHtmlElement }))
}))(SimpleOpportunityCard);
