import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import Carousel from 'centralized-design-system/src/Carousel';
import ConfigurableLoadingCard from 'centralized-design-system/src/SkeletonAnimations/ConfigurableLoadingCard/ConfigurableLoadingCard';
import RoleCard, { EmptyRoleCard } from '../../components/roleCard';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import {
  loadingCardRoleLayout,
  loadingCardDefaultLayout
} from '../../constants/loadingCardLayoutTypes';
import { JOB_TYPE, OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import {
  toggleActionFactory,
  DISMISS_ACTION,
  toggleAspirationsFactory
} from '@pages/TalentMarketplace/util';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';
import cn from 'classnames';
import './styles.scss';
import { SkillsContextProvider } from 'opportunity-marketplace/shared/SkillsContext';
import { useCountries } from '@pages/TalentMarketplace/shared/hooks';
import { useSelector } from 'react-redux';
import { LOCATION_USAGE_OPTIONS, isLocationVisible } from '@pages/TalentMarketplace/helpers';
import { NextInteractiveSuggestionContextProvider } from 'opportunity-marketplace/shared/context/NextInteractiveSuggestionContext';
import RecommendationFeedbackCard from '@components/roleCard/RecommendationFeedbackCard/RecommendationFeedbackCard';
import { useGetRecommendationFeedback } from '@components/roleCard/RecommendationFeedbackCard/hooks';

const RolesCarousel = ({
  titleIcon = '',
  fullCard = false,
  hideShowAll = false,
  hideIfNoData = false,
  noDataLink = '/career/job-roles',
  roles,
  setRoles,
  title,
  description = null,
  loading: rolesLoading,
  parentComponent = '',
  customPlaceholder,
  dismissable,
  showFeedbackCard,
  filterFunction = () => {
    return true;
  },
  displayedItems = 4,
  showCounter = true,
  showDevelopmentPlan = false,
  showDevelopmentPlanLink = false,
  hideSocialButtons = false
}) => {
  const navigate = useNavigate();
  const countries = useCountries();
  const [isFeedbackFilled, setIsFeedbackFilled] = useState(false);
  const [canUserFillFeedback] = useGetRecommendationFeedback(OPPORTUNITY_TYPE.JOB_ROLE);
  const locationsEnabled = useSelector(state => state.locationsConfiguration.get('enable'));
  const locationFieldVisibility = useSelector(state =>
    state.locationsConfiguration.get('visibility')
  );

  const isLocationShown =
    locationsEnabled &&
    isLocationVisible(locationFieldVisibility, LOCATION_USAGE_OPTIONS.JOB_ROLE_CARD);

  const componentTitle = title
    ? title
    : translatr('web.talentmarketplace.main', 'RecommendedOpportunities', {
        opportunities: omp('tm_tm_job_roles')
      });
  const [isActionPending, setIsActionPending] = useState(false);
  const { isAspirationalRole, updateAspirations, isAspirationsInitLoading } = useContext(
    AspirationsContext
  );

  const toggleDismiss = React.useMemo(
    () =>
      toggleActionFactory(
        JOB_TYPE.ROLE,
        DISMISS_ACTION,
        roles,
        setRoles,
        `RolesCarousel - ${parentComponent}`,
        setIsActionPending
      ),
    [roles]
  );

  const toggleAspirations = React.useMemo(
    () => toggleAspirationsFactory(updateAspirations, `RolesCarousel - ${parentComponent}`),
    [updateAspirations]
  );

  const actualCount = roles?.length ? roles.filter(filterFunction).length : null;

  if (hideIfNoData && !actualCount) {
    return false;
  }

  const renderCarouselChildren = () => {
    if (actualCount > 0) {
      return roles.filter(filterFunction).map((role, index) => {
        const detailUrl = `/career/detail/${JOB_TYPE.ROLE}/${encodeURIComponent(role.id)}`;
        return (
          <SkillsContextProvider key={role.id} initSkills={role.allSkills}>
            <NextInteractiveSuggestionContextProvider opportunities={roles} opportunity={role}>
              <li key={role.id}>
                <RoleCard
                  key={`role_card_${role.id}`}
                  {...role}
                  onClick={() =>
                    navigate(detailUrl, {
                      state: {
                        overallScore: role.overallScore,
                        skillsGraphScore: role.skillsGraphScore
                      }
                    })
                  }
                  detailUrl={detailUrl}
                  onDismiss={toggleDismiss}
                  rolesLoading={rolesLoading}
                  fullCard={fullCard}
                  isAspirationRole={isAspirationalRole(role.id)}
                  onAspirationChange={toggleAspirations}
                  role={role}
                  dismissable={dismissable}
                  showLocation={isLocationShown}
                  countries={countries}
                  isSuggestion
                  showDevelopmentPlanLink={showDevelopmentPlanLink}
                  isDevelopmentPlanEnabled={showDevelopmentPlan}
                  hideSocialButtons={hideSocialButtons}
                />
              </li>
              {!isFeedbackFilled &&
                canUserFillFeedback &&
                showFeedbackCard &&
                (index === 1 || roles.length === 1) && (
                  <li>
                    <RecommendationFeedbackCard
                      className="role-card"
                      opportunities={roles}
                      opportunityType={OPPORTUNITY_TYPE.JOB_ROLE}
                      onSubmit={() => setIsFeedbackFilled(true)}
                    />
                  </li>
                )}
            </NextInteractiveSuggestionContextProvider>
          </SkillsContextProvider>
        );
      });
    } else {
      return !customPlaceholder
        ? [...Array(displayedItems)].map((x, i) =>
            i === 0 ? (
              <EmptyRoleCard noDataLink={noDataLink} small type="role" />
            ) : (
              <div className="role-card-placeholder" />
            )
          )
        : [];
    }
  };

  return (
    <>
      <div className="role-carousel-header">
        <div className="role-carousel-header_left">
          <h2 className="role-carousel-header_left__title">
            {titleIcon && <span className={`role-carousel-header_left__icons ${titleIcon}`} />}
            {componentTitle}{' '}
            {showCounter && roles && actualCount > 0 && <span>({actualCount})</span>}
          </h2>
          {description && (
            <div
              className={cn('role-carousel-header_left__description', {
                'role-carousel-header--hasicon': titleIcon
              })}
            >
              {description}
            </div>
          )}
        </div>
        {!hideShowAll && (
          <div className="role-carousel-header_right">
            <button
              className="pointer supporting-text-color"
              onClick={e => {
                e.preventDefault();
                navigate('/career/job-roles');
              }}
            >
              {translatr('web.common.main', 'ViewAll')}
            </button>
          </div>
        )}
      </div>
      <div
        className={cn('role-carousel-body', {
          'is-action-pending': isActionPending
        })}
      >
        {!rolesLoading && !isAspirationsInitLoading && (
          <>
            <Carousel
              ariaLabelPrefix={componentTitle}
              className={cn({
                'carousel-hidden': customPlaceholder && !actualCount
              })}
              ariaLive={'off'}
            >
              {renderCarouselChildren()}
            </Carousel>
            {!actualCount && customPlaceholder}
          </>
        )}
        {rolesLoading && (
          <Carousel>
            {[...Array(displayedItems)].map((x, i) => (
              <ConfigurableLoadingCard
                key={`roles-loading-card-${i}`}
                containerStyles={
                  fullCard
                    ? loadingCardDefaultLayout.containerStyles
                    : loadingCardRoleLayout.containerStyles
                }
                boxItemsConfig={
                  fullCard
                    ? loadingCardDefaultLayout.boxItemsConfig
                    : loadingCardRoleLayout.boxItemsConfig
                }
              />
            ))}
          </Carousel>
        )}
      </div>
    </>
  );
};

RolesCarousel.propTypes = {
  fullCard: PropTypes.bool,
  titleIcon: PropTypes.string,
  hideShowAll: PropTypes.bool,
  hideIfNoData: PropTypes.bool,
  noDataLink: PropTypes.string,
  navigateToViewAll: PropTypes.func,
  title: PropTypes.string,
  description: PropTypes.string,
  loading: PropTypes.bool,
  setRoles: PropTypes.func,
  roles: PropTypes.array,
  parentComponent: PropTypes.string,
  customPlaceholder: PropTypes.element,
  filterFunction: PropTypes.func,
  customPush: PropTypes.func,
  dismissable: PropTypes.bool,
  displayedItems: PropTypes.number,
  showCounter: PropTypes.bool,
  showDevelopmentPlan: PropTypes.bool,
  showDevelopmentPlanLink: PropTypes.bool,
  hideSocialButtons: PropTypes.bool,
  showFeedbackCard: PropTypes.bool
};

export default RolesCarousel;
