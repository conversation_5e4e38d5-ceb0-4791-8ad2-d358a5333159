import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import kebabCase from 'lodash/kebabCase';
import cn from 'classnames';
import Carousel from 'centralized-design-system/src/Carousel';
import ConfigurableLoadingCard from 'centralized-design-system/src/SkeletonAnimations/ConfigurableLoadingCard/ConfigurableLoadingCard';
import './TMCarousel.scss';
import { loadingCardDefaultLayout } from '../../../constants/loadingCardLayoutTypes';
import {
  tmSaveSearchFilters,
  tmAddCustomFilterOption
} from '../../../actions/talentmarketplaceActions';
import { cleanupFilterNames } from '@pages/TalentMarketplace/shared/filters/Filters.utils';
import { ButtonAnchor } from 'centralized-design-system/src/Buttons';

const TMCarousel = ({
  title,
  description = null,
  children,
  itemCount = null,
  loading = false,
  loadingCardLayout = loadingCardDefaultLayout,
  viewAll = null,
  tooltip = null,
  titleIcon = '',
  displayedItems = 4,
  dispatch = () => {}
}) => {
  const navigate = useNavigate();

  const navigateTo = useCallback(
    e => {
      if (Object.keys(viewAll.filtersToApply).length) {
        const extractedFilters = cleanupFilterNames(viewAll.filtersToApply.filters);

        Object.keys(extractedFilters).forEach(function(key) {
          dispatch(
            tmAddCustomFilterOption({
              filterId: key,
              options: extractedFilters[key],
              bucketName: viewAll.filtersToApply.bucketName
            })
          );
        });
        dispatch(tmSaveSearchFilters(viewAll.filtersToApply));
      }
      if (viewAll) {
        navigate(viewAll.link);
        e.preventDefault();
      }
    },
    [dispatch, viewAll]
  );
  const idFromTitle = title.toLowerCase().replaceAll(' ', '-');
  return (
    <div className="tm-carousel ed-ui">
      <div className="tm-carousel__header">
        <div className="tm-carousel__header__left">
          {!!title && (
            <h2 className="tm-carousel__header__left__title">
              {titleIcon && <span className={`tm-carousel__header__left__icons ${titleIcon}`} />}
              {loading || itemCount === null ? (
                title
              ) : (
                <>
                  <span id={`tm-carousel-${kebabCase(title)}`}>{title}</span>
                  <span>({itemCount})</span>
                </>
              )}
              {!!tooltip && <i className="icon-info-circle" />}
            </h2>
          )}
          {description && (
            <div
              className={cn('tm-carousel__header__left__description', {
                'tm-carousel__header--hasicon': titleIcon
              })}
            >
              {description}
            </div>
          )}
        </div>
        {!!viewAll && (
          <div className="tm-carousel__header__right">
            <div className="tm-carousel__header__right__view-all">
              <ButtonAnchor
                color="primary"
                variant="borderless"
                padding="xsmall"
                size="medium"
                id={`tm-carousel-${kebabCase(title)}-link`}
                href={viewAll.link}
                handleClick={navigateTo}
                aria-labelledby={`tm-carousel-${kebabCase(title)} tm-carousel-${kebabCase(
                  title
                )}-link`}
              >
                {viewAll.label}
              </ButtonAnchor>
            </div>
          </div>
        )}
      </div>
      {loading && (
        <div className="tm-carousel__content ed-carousel-wrapper">
          <div className="ed-carousel">
            {[...Array(displayedItems)].map((x, i) => (
              <ConfigurableLoadingCard
                key={`tmcarousel-${idFromTitle}-loading-card-${i}`}
                containerStyles={loadingCardLayout.containerStyles}
                boxItemsConfig={loadingCardLayout.boxItemsConfig}
              />
            ))}
          </div>
        </div>
      )}
      {!loading && (
        <Carousel ariaLabelPrefix={title} className="tm-carousel__content" ariaLive={'off'}>
          {children}
        </Carousel>
      )}
    </div>
  );
};

TMCarousel.propTypes = {
  children: PropTypes.node,
  itemCount: PropTypes.number,
  loading: PropTypes.bool,
  loadingCardLayout: PropTypes.object,
  title: PropTypes.string,
  description: PropTypes.string,
  tooltip: PropTypes.node,
  viewAll: PropTypes.shape({
    label: PropTypes.string,
    link: PropTypes.string
  }),
  titleIcon: PropTypes.string,
  displayedItems: PropTypes.number
};

export default connect()(TMCarousel);
