# Calendar Events Modal - Accessibility Implementation

## Overview
This component implements a fully accessible calendar grid using semantic HTML table elements combined with ARIA Grid roles that complies with WCAG 2.1 Level AA standards and follows the W3C ARIA Grid pattern.

## Accessibility Features

### Semantic HTML Table Structure with ARIA Grid
- **Table Element**: `<table>` with `role="grid"` and descriptive `aria-label`
- **Table Header**: `<thead><tr><th>` elements with `role="columnheader"` for weekday headers
- **Table Body**: `<tbody><tr><td>` elements with `role="row"` and `role="gridcell"` for calendar structure
- **Native Table Semantics**: Provides inherent accessibility benefits while enhanced with ARIA grid roles

### Keyboard Navigation
- **Arrow Keys**: Navigate between calendar cells
  - `ArrowLeft/Right`: Move horizontally between days
  - `ArrowUp/Down`: Move vertically between weeks
- **Home/End**: Jump to first/last cell in grid
- **Tab**: Follows standard tab order through interactive elements

### Focus Management
- Initial focus set to today's date (if visible) or first day of current month
- Only one cell is focusable at a time (`tabindex="0"` for focused, `-1` for others)
- Visual focus indicator with 2px outline
- Focus updates dynamically during keyboard navigation

### ARIA States and Labels
- `aria-selected="true"` for today's date
- Comprehensive `aria-label` for each cell:
  - Date information: "15 December 2023"
  - Event count: ", 3 events" (if applicable)
  - Today indicator: ", today" (if applicable)
- Grid container labeled with month and year
- Column headers with full day names (Sunday, Monday, etc.)

### Screen Reader Support
- Semantic table structure provides natural reading order
- All interactive elements properly labeled
- Event information announced when navigating to cells with events
- Clear distinction between current month and previous/next month dates

## Implementation Details

### HTML Structure
```html
<table role="grid" aria-label="December 2023 Calendar">
  <thead>
    <tr role="row">
      <th role="columnheader" aria-label="Sunday">S</th>
      <!-- ... other weekdays ... -->
    </tr>
  </thead>
  <tbody>
    <tr role="row">
      <td role="gridcell" tabindex="0" aria-selected="true" 
          aria-label="15 December 2023, 2 events, today">
        <span aria-hidden="true">15</span>
        <!-- event content -->
      </td>
      <!-- ... other days ... -->
    </tr>
    <!-- ... other weeks ... -->
  </tbody>
</table>
```

### CSS Considerations
- Uses `border-collapse: collapse` for proper table styling
- `vertical-align: top` for consistent cell alignment
- Focus styles with `outline` for keyboard navigation
- Responsive design maintains accessibility across devices

## Usage Example

```jsx
import CalendarEvents from 'centralized-design-system/src/Modals/CalendarEvents';

function MyComponent() {
  const [showCalendar, setShowCalendar] = useState(false);
  
  return (
    <CalendarEvents 
      show={showCalendar} 
      callback={setShowCalendar} 
    />
  );
}
```

## Testing with Screen Readers

### JAWS Testing
1. Open calendar modal
2. Navigate to calendar grid
3. Use arrow keys to move between dates
4. Verify proper announcement of dates, events, and today indicator

### NVDA Testing
1. Similar to JAWS testing
2. Ensure table structure is properly announced
3. Verify keyboard navigation works as expected

## Compliance

This implementation meets:
- **WCAG 2.1 Level AA** standards
- **W3C ARIA Grid Pattern** guidelines
- **Section 508** requirements
- **EN 301 549** accessibility standards

## Benefits of Table + ARIA Approach

1. **Semantic Foundation**: Native table semantics provide inherent accessibility
2. **Enhanced Navigation**: ARIA grid roles add advanced keyboard navigation
3. **Screen Reader Compatibility**: Works well with all major screen readers
4. **Maintainable Code**: Clear structure that's easy to understand and modify
5. **Future-Proof**: Combines best of semantic HTML and modern ARIA patterns

## References
- [W3C ARIA Grid Pattern](https://www.w3.org/WAI/ARIA/apg/patterns/grid/)
- [W3C Data Grid Examples](https://www.w3.org/WAI/ARIA/apg/patterns/grid/examples/data-grids/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [HTML Table Accessibility](https://www.w3.org/WAI/tutorials/tables/)
